package fr.enedis.i2r.si.mock;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

public class MetricsJsonValidatorTest {

    @Test
    public void un_json_contenant_un_seul_objet_statcellserving_est_valide() {
        String containerWithStatCellServing = """
            {
                "class": "Container",
                "cmdKey": null,
                "emitted": "2025-09-22T13:37:41Z",
                "entity": "LOGDM",
                "flow": "/db/cfg/flows/stat",
                "modified": "2025-09-21T22:00:02Z",
                "name": "test",
                "nb": 1,
                "pub": true,
                "objects": [
                    {
                        "class": "StatCellServing",
                        "cmdKey": null,
                        "emitted": "2025-09-22T13:37:41Z",
                        "entity": "LOGDM",
                        "flow": null,
                        "modified": "2025-09-21T21:59:46Z",
                        "name": "StatCellServing",
                        "pub": true,
                        "dateMes": "2025-09-20T22:00:02Z",
                        "earfcn": 1850,
                        "mcc": 208,
                        "mnc": 1,
                        "pci": 123,
                        "rsrp": -85,
                        "rsrq": -12,
                        "rssi": -65,
                        "sinr": 15,
                        "tac": 456,
                        "techno": "4G"
                    }
                ]
            }
            """;

        boolean isValid = JsonValidator.validateMetricsBoitier(containerWithStatCellServing);

        assertTrue(isValid, "A JSON with only StatCellServing should validate against the schema");
    }

    @Test
    public void un_json_contenant_plusieurs_objets_est_valide() {
        String containerWithStatWAN = """
            {
                "class": "Container",
                "cmdKey": null,
                "emitted": "2025-09-22T13:37:41Z",
                "entity": "LOGDM",
                "flow": "/db/cfg/flows/stat",
                "modified": "2025-09-21T22:00:02Z",
                "name": "test",
                "nb": 1,
                "pub": true,
                "objects": [
                    {
                        "class": "StatWAN",
                        "bpsRxAv": 14,
                        "bpsRxMax": 6891,
                        "bpsRxMin": 0,
                        "bpsTxAv": 20,
                        "bpsTxMax": 11020,
                        "bpsTxMin": 0,
                        "cmdKey": null,
                        "date": "2025-09-20T22:00:02Z",
                        "duration": 86398,
                        "emitted": "2025-09-22T13:37:41Z",
                        "entity": "LOGDM",
                        "flow": null,
                        "httpTmo": 0,
                        "logs": [],
                        "modified": "2025-09-21T21:59:46Z",
                        "name": "StatWAN",
                        "nbOctets": 29394097,
                        "nbOctetsCourant": 9134998,
                        "pdpKO": 1,
                        "pdpOK": 3,
                        "pub": true,
                        "rxHttpAPI": 0,
                        "rxHttpDownload": 0,
                        "rxHttpWeb": 73396,
                        "rxNtp": 400,
                        "rxPing": 24360,
                        "rxSsh": 0,
                        "rxTotal": 123694,
                        "tcpCnxKO": 0,
                        "tcpCnxOK": 0,
                        "tcpDisconnect": 0,
                        "tcpTmo": 0,
                        "txHttpAPI": 0,
                        "txHttpDownload": 0,
                        "txHttpWeb": 153922,
                        "txNtp": 400,
                        "txPing": 24528,
                        "txSsh": 0,
                        "txTotal": 205056
                    },
                    {
                        "class": "StatAlarms",
                        "cmdKey": null,
                        "date": "2025-09-20T22:00:02Z",
                        "duration": 86398,
                        "emitted": "2025-09-22T13:37:41Z",
                        "entity": "LOGDM",
                        "flow": null,
                        "ignored": 0,
                        "lost": 0,
                        "modified": "2025-09-21T21:59:46Z",
                        "name": "StatAlarms",
                        "notSent": 0,
                        "pub": true
                    }
                ]
            }
            """;

        boolean isValid = JsonValidator.validateMetricsBoitier(containerWithStatWAN);

        assertTrue(isValid, "A JSON with only StatWAN should validate against the schema");
    }

    @Test
    public void un_json_minimal_est_valide() {
        String minimalJson = """
            {
                "class": "Container",
                "cmdKey": null,
                "emitted": "2025-09-22T13:37:41Z",
                "entity": "LOGDM",
                "flow": "/db/cfg/flows/stat",
                "modified": "2025-09-21T22:00:02Z",
                "name": "test",
                "nb": 0,
                "objects": [],
                "pub": true
            }
            """;

        boolean isValid = JsonValidator.validateMetricsBoitier(minimalJson);

        assertTrue(isValid, "A minimal JSON should validate against the schema");
    }

    @Test
    public void un_json_incomplet_n_est_pas_valide() {
        String incompleteJson = """
            {
                "class": "Container",
                "entity": "LOGDM",
                "name": "test",
                "pub": false
            }
            """;

        boolean isValid = JsonValidator.validateMetricsBoitier(incompleteJson);

        assertFalse(isValid, "An incomplete JSON should not validate against the schema");
    }

    @Test
    public void un_json_vide_n_est_pas_valide() {
        String emptyJson = "{ }";

        boolean isValid = JsonValidator.validateMetricsBoitier(emptyJson);

        assertFalse(isValid, "An empty JSON should not validate against the schema");
    }
}
