package fr.enedis.i2r.si.mock;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.net.http.HttpRequest;

import org.junit.jupiter.api.Test;

class HttpRequestBuilderTest {

    @Test
    void create_cree_builder_avec_headers_de_base() {
        String uri = "https://localhost/test";

        HttpRequest request = HttpRequestBuilder.create(uri)
                .buildGet();

        assertEquals(uri, request.uri().toString());
        assertTrue(request.headers().firstValue("Accept").isPresent());
        assertTrue(request.headers().firstValue("Accept-Encoding").isPresent());
        assertTrue(request.headers().firstValue("User-Agent").isPresent());
    }

    @Test
    void withApiVersion_ajoute_header_version_api() {
        String uri = "https://localhost/test";

        HttpRequest request = HttpRequestBuilder.create(uri)
                .withApiVersion()
                .buildGet();

        assertTrue(request.headers().firstValue("X-ERDF-API-VERSION").isPresent());
        assertEquals("2.0", request.headers().firstValue("X-ERDF-API-VERSION").get());
    }

    @Test
    void withIdms_ajoute_header_idms() {
        String uri = "https://localhost/test";
        String idms = "12345678901234567890123456789012";

        HttpRequest request = HttpRequestBuilder.create(uri)
                .withIdms(idms)
                .buildGet();

        assertTrue(request.headers().firstValue("x-idms").isPresent());
        assertEquals(idms, request.headers().firstValue("x-idms").get());
    }

    @Test
    void withHash_ajoute_header_hash() {
        String uri = "https://localhost/test";
        String hash = "test-hash-123";

        HttpRequest request = HttpRequestBuilder.create(uri)
                .withHash(hash)
                .buildGet();

        assertTrue(request.headers().firstValue("X-ERDF-HASH").isPresent());
        assertEquals(hash, request.headers().firstValue("X-ERDF-HASH").get());
    }

    @Test
    void withPlainTextContentType_ajoute_header_content_type_plain_text() {
        String uri = "https://localhost/test";

        HttpRequest request = HttpRequestBuilder.create(uri)
                .withPlainTextContentType()
                .buildGet();

        assertTrue(request.headers().firstValue("Content-Type").isPresent());
        assertEquals("text/plain;charset=ISO-8859-1", request.headers().firstValue("Content-Type").get());
    }

    @Test
    void buildPut_cree_requete_put_avec_body() {
        String uri = "https://localhost/test";
        String body = "test body";

        HttpRequest request = HttpRequestBuilder.create(uri)
                .buildPut(body);

        assertEquals("PUT", request.method());
        assertEquals(uri, request.uri().toString());
    }

    @Test
    void buildPost_cree_requete_post_avec_body() {
        String uri = "https://localhost/test";
        String body = "test body";

        HttpRequest request = HttpRequestBuilder.create(uri)
                .buildPost(body);

        assertEquals("POST", request.method());
        assertEquals(uri, request.uri().toString());
    }

    @Test
    void buildGet_cree_requete_get() {
        String uri = "https://localhost/test";

        HttpRequest request = HttpRequestBuilder.create(uri)
                .buildGet();

        assertEquals("GET", request.method());
        assertEquals(uri, request.uri().toString());
    }

    @Test
    void methodes_chainees_ajoutent_tous_les_headers() {
        String uri = "https://localhost/test";
        String idms = "12345678901234567890123456789012";
        String hash = "test-hash-123";

        HttpRequest request = HttpRequestBuilder.create(uri)
                .withApiVersion()
                .withIdms(idms)
                .withHash(hash)
                .withPlainTextContentType()
                .buildGet();

        assertTrue(request.headers().firstValue("X-ERDF-API-VERSION").isPresent());
        assertTrue(request.headers().firstValue("x-idms").isPresent());
        assertTrue(request.headers().firstValue("X-ERDF-HASH").isPresent());
        assertTrue(request.headers().firstValue("Content-Type").isPresent());
        assertEquals("2.0", request.headers().firstValue("X-ERDF-API-VERSION").get());
        assertEquals(idms, request.headers().firstValue("x-idms").get());
        assertEquals(hash, request.headers().firstValue("X-ERDF-HASH").get());
        assertEquals("text/plain;charset=ISO-8859-1", request.headers().firstValue("Content-Type").get());
    }
}
