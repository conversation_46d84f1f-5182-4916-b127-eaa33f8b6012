package fr.enedis.i2r.si.mock;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.zip.GZIPOutputStream;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.google.common.net.HttpHeaders;

import fr.enedis.i2r.infra.rest.SiHeaders;
import fr.enedis.i2r.infra.rest.si.ao2.ConfigurationBoitierIcare;
import io.javalin.http.Context;
import io.javalin.json.JsonMapper;

class JsonExtractorTest {

    @Mock
    private Context context;

    @Mock
    private JsonMapper jsonMapper;

    private static final String VALID_JSON_CONFIG = """
        {
            "class": "Container",
            "name": "cfg",
            "objects": [
                {
                    "class": "DM",
                    "name": "dm",
                    "state": 2
                }
            ],
            "nb": 1
        }
        """;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void recuperation_configuration_avec_contenu_non_compresse_retourne_configuration() {
        ConfigurationBoitierIcare expectedConfig = new ConfigurationBoitierIcare();

        when(context.header(HttpHeaders.CONTENT_ENCODING)).thenReturn(null);
        when(context.bodyAsClass(ConfigurationBoitierIcare.class)).thenReturn(expectedConfig);

        ConfigurationBoitierIcare result = JsonExtractor.getConfigurationBoitier(context);

        assertNotNull(result);
        assertEquals(expectedConfig, result);
    }

    @Test
    void recuperation_configuration_avec_contenu_compresse_gzip_retourne_configuration() throws IOException {
        ConfigurationBoitierIcare expectedConfig = new ConfigurationBoitierIcare();
        InputStream compressedStream = createGzipInputStream(VALID_JSON_CONFIG);

        when(context.header(HttpHeaders.CONTENT_ENCODING)).thenReturn(SiHeaders.Constants.ZIP_FORMAT);
        when(context.bodyInputStream()).thenReturn(compressedStream);
        when(context.jsonMapper()).thenReturn(jsonMapper);
        when(jsonMapper.fromJsonString(any(String.class),
                                      eq(ConfigurationBoitierIcare.class))).thenReturn(expectedConfig);

        ConfigurationBoitierIcare result = JsonExtractor.getConfigurationBoitier(context);

        assertNotNull(result);
        assertEquals(expectedConfig, result);
    }

    @Test
    void recuperation_configuration_avec_encodage_null_utilise_body_direct() {
        ConfigurationBoitierIcare expectedConfig = new ConfigurationBoitierIcare();

        when(context.header(HttpHeaders.CONTENT_ENCODING)).thenReturn(null);
        when(context.bodyAsClass(ConfigurationBoitierIcare.class)).thenReturn(expectedConfig);

        ConfigurationBoitierIcare result = JsonExtractor.getConfigurationBoitier(context);

        assertNotNull(result);
        assertEquals(expectedConfig, result);
    }

    @Test
    void recuperation_configuration_avec_exception_lors_decompression_leve_runtime_exception() throws IOException {
        InputStream invalidGzipStream = new ByteArrayInputStream("invalid gzip data".getBytes());

        when(context.header(HttpHeaders.CONTENT_ENCODING)).thenReturn(SiHeaders.Constants.ZIP_FORMAT);
        when(context.bodyInputStream()).thenReturn(invalidGzipStream);

        assertThrows(RuntimeException.class, () -> {
            JsonExtractor.getConfigurationBoitier(context);
        });
    }

    @Test
    void recuperation_configuration_avec_json_mapper_exception_leve_runtime_exception() throws IOException {
        InputStream compressedStream = createGzipInputStream(VALID_JSON_CONFIG);

        when(context.header(HttpHeaders.CONTENT_ENCODING)).thenReturn(SiHeaders.Constants.ZIP_FORMAT);
        when(context.bodyInputStream()).thenReturn(compressedStream);
        when(context.jsonMapper()).thenReturn(jsonMapper);
        when(jsonMapper.fromJsonString(any(String.class), eq(ConfigurationBoitierIcare.class)))
                .thenThrow(new RuntimeException("JSON parsing error"));

        assertThrows(RuntimeException.class, () -> {
            JsonExtractor.getConfigurationBoitier(context);
        });
    }

    @Test
    void decompression_avec_contenu_valide_retourne_json_decompresse() throws IOException {
        String originalJson = VALID_JSON_CONFIG;
        InputStream compressedStream = createGzipInputStream(originalJson);

        String result = JsonExtractor.decompressGzip(compressedStream);

        assertNotNull(result);
        // The method concatenates lines without preserving line breaks but keeps other whitespace
        String expectedResult = originalJson.replace("\n", "").replace("\r", "");
        assertEquals(expectedResult, result);
    }

    @Test
    void decompression_avec_stream_invalide_leve_exception() {
        InputStream invalidStream = new ByteArrayInputStream("not gzip data".getBytes());

        assertThrows(IOException.class, () -> {
            JsonExtractor.decompressGzip(invalidStream);
        });
    }

    private InputStream createGzipInputStream(String content) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOut = new GZIPOutputStream(baos)) {
            gzipOut.write(content.getBytes(StandardCharsets.UTF_8));
        }
        return new ByteArrayInputStream(baos.toByteArray());
    }
}
