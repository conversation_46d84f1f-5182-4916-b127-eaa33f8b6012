package fr.enedis.i2r.si.mock;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import fr.enedis.i2r.infra.rest.SiHeaders;
import io.javalin.http.Context;

class BipCallerTest {

    @Mock
    private HttpClient httpClient;

    @Mock
    private Context context;

    @Mock
    private HttpResponse<String> httpResponse;

    private BipCaller bipCaller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        bipCaller = new BipCaller(httpClient);
    }

    @Test
    void changement_statut_bip_avec_contexte_valide_envoie_requete_http() throws IOException, InterruptedException {
        String configHash = "test-hash-123";
        String idms = "12345678901234567890123456789012";

        when(context.header(SiHeaders.Constants.HASH_HEADER)).thenReturn(configHash);
        when(context.pathParam("idms")).thenReturn(idms);
        when(httpClient.<String>send(any(HttpRequest.class), any()))
                .thenReturn(httpResponse);
        when(httpResponse.statusCode()).thenReturn(200);
        when(httpResponse.body()).thenReturn("OK");
        when(httpResponse.headers()).thenReturn(mock(java.net.http.HttpHeaders.class));
        when(httpResponse.headers().firstValue("Content-Type")).thenReturn(java.util.Optional.of("application/json"));

        bipCaller.setStableStatus(context);

        verify(httpClient).send(any(HttpRequest.class), any());
    }

    @Test
    void changement_statut_bip_sans_hash_configuration_leve_exception() {
        when(context.header(SiHeaders.Constants.HASH_HEADER)).thenReturn(null);

        assertThrows(IllegalArgumentException.class, () -> {
            bipCaller.setStableStatus(context);
        });
    }


    @Test
    void changement_statut_bip_avec_exception_http_propage_exception() throws IOException, InterruptedException {
        String configHash = "test-hash-123";
        String idms = "12345678901234567890123456789012";

        when(context.header(SiHeaders.Constants.HASH_HEADER)).thenReturn(configHash);
        when(context.pathParam("idms")).thenReturn(idms);
        when(httpClient.<String>send(any(HttpRequest.class), any()))
                .thenThrow(new IOException("Network error"));

        assertThrows(IOException.class, () -> {
            bipCaller.setStableStatus(context);
        });
    }

    @Test
    void demande_config_envoie_requete_get_pour_configuration_complete() throws IOException, InterruptedException {
        when(httpClient.<String>send(any(HttpRequest.class), any()))
                .thenReturn(httpResponse);
        when(httpResponse.statusCode()).thenReturn(200);
        when(httpResponse.body()).thenReturn("Configuration data");
        when(httpResponse.headers()).thenReturn(mock(java.net.http.HttpHeaders.class));
        when(httpResponse.headers().firstValue("Content-Type")).thenReturn(java.util.Optional.of("application/json"));

        bipCaller.requestFullCfg(context);

        verify(httpClient).send(any(HttpRequest.class), any());
    }

    @Test
    void demande_config_avec_exception_http_propage_exception() throws IOException, InterruptedException {
        when(httpClient.<String>send(any(HttpRequest.class), any()))
                .thenThrow(new InterruptedException("Request interrupted"));

        assertThrows(InterruptedException.class, () -> {
            bipCaller.requestFullCfg(context);
        });
    }
}
