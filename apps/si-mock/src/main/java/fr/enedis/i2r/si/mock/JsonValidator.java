package fr.enedis.i2r.si.mock;

import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.networknt.schema.InputFormat;
import com.networknt.schema.JsonSchema;
import com.networknt.schema.JsonSchemaFactory;
import com.networknt.schema.SchemaLocation;
import com.networknt.schema.SpecVersion.VersionFlag;
import com.networknt.schema.ValidationMessage;

public class JsonValidator {

    private static final Logger logger = LoggerFactory.getLogger(JsonValidator.class);

    public static boolean validateConfigurationBoitier(String jsonConfig) {
        return validateJsonAgainstSchema("classpath:schema/icom-config.schema.json", jsonConfig);
    }

    public static boolean validateMetricsBoitier(String jsonMetrics) {
        return validateJsonAgainstSchema("classpath:schema/icom-metrics.schema.json", jsonMetrics);
    }

    private static boolean validateJsonAgainstSchema(String jsonSchemaPath, String jsonInput) {
        JsonSchemaFactory factory = JsonSchemaFactory.getInstance(VersionFlag.V202012);
        JsonSchema schemaFromClasspath = factory.getSchema(SchemaLocation.of(jsonSchemaPath));

        Set<ValidationMessage> errors = schemaFromClasspath.validate(jsonInput, InputFormat.JSON);

        if (errors.size() > 0) {
            logger.error("Erreur de validation du schema JSON. Nombre d'erreurs: {}", errors.size());
            for (ValidationMessage error : errors) {
                logger.error("Erreur de validation: {}", error.getMessage());
            }
            return false;
        }
        return true;
    }
}
