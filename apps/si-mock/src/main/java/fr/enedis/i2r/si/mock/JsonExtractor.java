package fr.enedis.i2r.si.mock;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.zip.GZIPInputStream;

import com.google.common.net.HttpHeaders;

import fr.enedis.i2r.infra.rest.SiHeaders;
import fr.enedis.i2r.infra.rest.si.ao2.ConfigurationBoitierIcare;
import io.javalin.http.Context;

public class JsonExtractor {

    static ConfigurationBoitierIcare getConfigurationBoitier(Context ctx) {
        ConfigurationBoitierIcare config;
        try {
            if (SiHeaders.Constants.ZIP_FORMAT.equalsIgnoreCase(ctx.header(HttpHeaders.CONTENT_ENCODING))) {
                String decompressedJson = decompressGzip(ctx.bodyInputStream());
                config = ctx.jsonMapper().fromJsonString(decompressedJson, ConfigurationBoitierIcare.class);
            } else {
                config = ctx.bodyAsClass(ConfigurationBoitierIcare.class);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return config;
    }

    static String decompressGzip(InputStream compressedStream) throws IOException {
        try (GZIPInputStream gis = new GZIPInputStream(compressedStream);
                InputStreamReader reader = new InputStreamReader(gis, StandardCharsets.UTF_8);
                BufferedReader in = new BufferedReader(reader)) {

            StringBuilder jsonBuilder = new StringBuilder();
            String line;
            while ((line = in.readLine()) != null) {
                jsonBuilder.append(line);
            }
            return jsonBuilder.toString();
        }
    }
}
