package fr.enedis.i2r.si.mock;

public final class Constants {

    private Constants() {
        throw new UnsupportedOperationException("Utility class");
    }

    // Server Configuration
    public static final int SECURE_PORT = 8443;
    public static final int INSECURE_PORT = 8440;
    public static final String KEYSTORE_PASSWORD = "password";

    // SSL Configuration Paths
    public static final String KEYSTORE_PATH = "/var/lib/i2r/si-mock-certificate.jks";
    public static final String TRUSTSTORE_PATH = "/var/lib/i2r/si-mock-truststore.jks";

    // API Information
    public static final String API_TITLE = "OpenAPI SI Mock";
    public static final String API_VERSION = "1.0.0";
    public static final String API_DESCRIPTION = "Documentation de l'API du Mock SI (iCom/iCoeur/iCare)";

    // BIP Communication
    public static final String BIP_HOST = "127.0.0.1";
    public static final String TEST_IDMS = "475000DE007860000010000001000223";
    public static final int IDMS_LENGTH = 32;
    public static final String STABLE_STATUS_PAYLOAD = "{\"name\":\"dm\",\"class\":\"DM\",\"state\":2}";

    public static final String[] ASCII_ART = {
        " ▄▀▀ █    █▄ ▄█ ▄▀▄ ▄▀▀ █▄▀     ▄▀▄ █▄ █",
        " ▄██ █ ▀▀ █ ▀ █ ▀▄▀ ▀▄▄ █ █     ▀▄▀ █ ▀█"
    };

    public static final class HeadersValues {
        private HeadersValues() {}

        public static final String ACCEPT = "text/plain, application/json, application/yaml, application/*+json, */*";
        public static final String ACCEPT_ENCODING = "gzip, x-gzip, deflate";
        public static final String CONTENT_TYPE_PLAIN = "text/plain;charset=ISO-8859-1";
        public static final String USER_AGENT = "Apache-HttpClient/5.4.2 (Java/17.0.3)";
    }
}
