package fr.enedis.i2r.si.mock;

import java.io.IOException;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.net.HttpHeaders;
import com.networknt.schema.utils.StringUtils;

import fr.enedis.i2r.infra.rest.SiHeaders;
import fr.enedis.i2r.infra.rest.si.ao2.ConfigurationBoitierIcare;
import fr.enedis.i2r.infra.rest.si.ao2.metrics.MetricsBoitier;
import fr.enedis.i2r.infra.rest.si.error.ErrorCode;
import fr.enedis.i2r.infra.rest.si.error.ErrorDetail;
import fr.enedis.i2r.infra.rest.si.error.IComError;
import fr.enedis.i2r.infra.rest.si.error.Message;
import fr.enedis.i2r.infra.rest.si.error.Origin;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;
import io.javalin.openapi.HttpMethod;
import io.javalin.openapi.OpenApi;
import io.javalin.openapi.OpenApiContent;
import io.javalin.openapi.OpenApiParam;
import io.javalin.openapi.OpenApiRequestBody;
import io.javalin.openapi.OpenApiResponse;

public class SiController {
    private static final Logger logger = LoggerFactory.getLogger(SiController.class);

    @OpenApi(
        summary = "Reçoit et valide la configuration du boitier par le SI. Si la configuration est OK, le main tente de passer le boitier en statut \"STABLE\"",
        operationId = "receiveConfiguration",
        path = "/SIService/{idms}/db/cfg", // Javalin/Swagger constant constraint
        methods = HttpMethod.PUT,
        tags = { "Configuration" },
        pathParams = {
            @OpenApiParam(
                name = "idms",
                type = String.class,
                required = true,
                description = "ID Module Sécurité"
            )
        },
        requestBody = @OpenApiRequestBody(
                    content = {
                            @OpenApiContent(from = ConfigurationBoitierIcare.class) },
                    required = true,
            description = "Configuration du boitier"
        ),
        responses = {
            @OpenApiResponse(
                status = "200",
                description = "Configuration valide",
                content = { @OpenApiContent(from = String.class) }
            ),
            @OpenApiResponse(
                status = "400",
                description = "IDMS manquant",
                content = { @OpenApiContent(from = String.class) }
            ),
            @OpenApiResponse(
                status = "500",
                description = "Configuration invalide",
                content = { @OpenApiContent(from = String.class) }
            )
        }
    )
    public void receiveConfiguration(Context ctx) {
        String idms = ctx.pathParam("idms");

        logger.info("Receive configuration");
        logger.info("idms : {}", idms);
        logger.info("");

        if (!isValidIdms(idms)) {
            ctx.status(HttpStatus.BAD_REQUEST.getCode()).result("IDMS is invalid");
            return;
        }

        String config;
        try {
            config = extractBody(ctx);
        } catch (IOException e) {
            logger.error("Failed to extract configuration", e);
            ctx.status(HttpStatus.BAD_REQUEST.getCode()).result("Invalid body data.");
            return;
        }

        logger.info("Configuration received: {}", config);

        HttpStatus status;
        String result = "";
        if (JsonValidator.validateConfigurationBoitier(config)) {
            status = HttpStatus.OK;
            result = HttpStatus.OK.toString();
        } else {
            status = HttpStatus.BAD_REQUEST;
            result = buildError("Configuration boitier invalide.");
        }
        ctx.status(status).result(result);
    }

    @OpenApi(
        summary = "Reçoit les métriques du boitier.",
        operationId = "receiveMetrics",
        path = "/SIService/{idms}/db/stats",
        methods = HttpMethod.PUT,
        tags = { "Metrics" },
        pathParams = {
            @OpenApiParam(
                name = "idms",
                type = String.class,
                required = true,
                description = "ID Module Sécurité"
            )
        },
        requestBody = @OpenApiRequestBody(
                    content = {
                            @OpenApiContent(from = MetricsBoitier.class) },
                    required = true,
            description = "Metriques du boitier"
        ),
        responses = {
            @OpenApiResponse(
                status = "200",
                description = "Métriques valides",
                content = { @OpenApiContent(from = String.class) }
            ),
            @OpenApiResponse(
                status = "400",
                description = "Requête invalide",
                content = { @OpenApiContent(from = String.class) }
            ),
            @OpenApiResponse(
                status = "500",
                description = "Erreur interne",
                content = { @OpenApiContent(from = String.class) }
            )
        }
    )
    public void receiveMetrics(Context ctx) {
        String idms = ctx.pathParam("idms");

        logger.info("Receive metrics");
        logger.info("idms : {}", idms);
        logger.info("");

        if (!isValidIdms(idms)) {
            ctx.status(HttpStatus.BAD_REQUEST.getCode()).result("IDMS is invalid");
            return;
        }

        String metrics;
        try {
            metrics = extractBody(ctx);
        } catch (IOException e) {
            ctx.status(HttpStatus.BAD_REQUEST.getCode()).result("Le corps de requête est invalide.");
            return;
        }
        logger.info(metrics);

        HttpStatus status;
        String result = "";
        if (JsonValidator.validateMetricsBoitier(metrics)) {
            status = HttpStatus.OK;
            result = HttpStatus.OK.toString();
        } else {
            status = HttpStatus.BAD_REQUEST;
            result = buildError("Métriques du boitier invalides.");
        }
        ctx.status(status).result(result);
    }

    private static String buildError(String errorMessage) {
        String jsonResponse = "";
        try {
            var errorDetail = new ErrorDetail();
            errorDetail.setCode(ErrorCode.BAD_CONTENT);
            errorDetail.setMessage(errorMessage);
            var message = new Message();
            message.setBody("Error SI Mock");
            var error = new IComError();
            error.setOrigin(Origin.ICOEUR);
            error.setMessage(message);
            error.setErrors(List.of(errorDetail));

            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
            jsonResponse = objectMapper.writeValueAsString(error);
            return jsonResponse;
        } catch (Exception ex) {
            logger.error("Failed to build error response", ex);
        }
        return jsonResponse;
    }

    private boolean isValidIdms(String idms) {
        return StringUtils.isNotBlank(idms) && idms.length() == 32;
    }

    private String extractBody(Context ctx) throws IOException {
        if (SiHeaders.Constants.ZIP_FORMAT.equalsIgnoreCase(ctx.header(HttpHeaders.CONTENT_ENCODING))) {
            return JsonExtractor.decompressGzip(ctx.bodyInputStream());
        } else {
            return ctx.body();
        }
    }
}
