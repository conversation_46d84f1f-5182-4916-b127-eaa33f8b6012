package fr.enedis.i2r.si.mock;

import java.net.URI;
import java.net.http.HttpRequest;
import java.net.http.HttpRequest.BodyPublishers;

import com.google.common.net.HttpHeaders;

import fr.enedis.i2r.infra.rest.SiHeaders;

public final class HttpRequestBuilder {

    private final HttpRequest.Builder requestBuilder;

    private HttpRequestBuilder(String uri) {
        this.requestBuilder = HttpRequest.newBuilder()
                .uri(URI.create(uri))
                .header(HttpHeaders.ACCEPT, Constants.HeadersValues.ACCEPT)
                .header(HttpHeaders.ACCEPT_ENCODING, Constants.HeadersValues.ACCEPT_ENCODING)
                .header(HttpHeaders.USER_AGENT, Constants.HeadersValues.USER_AGENT);
    }

    public static HttpRequestBuilder create(String uri) {
        return new HttpRequestBuilder(uri);
    }

    public HttpRequestBuilder withApiVersion() {
        requestBuilder.header(SiHeaders.Constants.API_VERSION_HEADER, SiHeaders.Constants.API_VERSION_VALUE);
        return this;
    }

    public HttpRequestBuilder withIdms(String idms) {
        requestBuilder.header(SiHeaders.Constants.IDMS_HEADER, idms);
        return this;
    }

    public HttpRequestBuilder withHash(String hash) {
        requestBuilder.header(SiHeaders.Constants.HASH_HEADER, hash);
        return this;
    }

    public HttpRequestBuilder withPlainTextContentType() {
        requestBuilder.header(HttpHeaders.CONTENT_TYPE, Constants.HeadersValues.CONTENT_TYPE_PLAIN);
        return this;
    }

    public HttpRequestBuilder withHeader(String name, String value) {
        requestBuilder.header(name, value);
        return this;
    }

    public HttpRequest buildGet() {
        return requestBuilder.GET().build();
    }

    public HttpRequest buildPost(String body) {
        return requestBuilder.POST(BodyPublishers.ofString(body)).build();
    }

    public HttpRequest buildPut(String body) {
        return requestBuilder.PUT(BodyPublishers.ofString(body)).build();
    }

}
