package fr.enedis.i2r.si.mock;

import static io.javalin.http.HttpStatus.OK;

import java.io.FileInputStream;
import java.net.http.HttpClient;
import java.security.KeyStore;
import java.security.SecureRandom;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.infra.rest.si.SiEndpoint;
import io.javalin.Javalin;
import io.javalin.community.ssl.SslPlugin;
import io.javalin.config.JavalinConfig;
import io.javalin.openapi.plugin.OpenApiPlugin;
import io.javalin.openapi.plugin.swagger.SwaggerPlugin;

public class Main {

    private static final Logger logger = LoggerFactory.getLogger(Main.class);

    public static void main(String[] args) {
        logger.debug("Starting SI Mock...");

        Javalin app = Javalin.create(Main::configureHttpServer);
        HttpClient httpClient = createHttpClientWithTrustStore();

        setupRoutes(app, httpClient);
        app.start();

        logServerInfo();
        printAsciiArt();
    }

    private static void setupRoutes(Javalin app, HttpClient httpClient) {
        var siController = new SiController();
        var bipCaller = new BipCaller(httpClient);

        app.put(SiEndpoint.SendConfig.endpointTemplate, ctx -> {
            siController.receiveConfiguration(ctx);
            if (ctx.status() == OK) {
                bipCaller.setStableStatus(ctx);
            }
        });

        app.put(SiEndpoint.SendMetrics.endpointTemplate, siController::receiveMetrics);

        app.get("trigger-cfg-request", ctx -> {
            bipCaller.requestFullCfg(ctx);
        });
    }

    private static void logServerInfo() {
        logger.debug("REST Server has started and is running...");
        logger.info("Swagger docs at https://localhost:" + Constants.SECURE_PORT + "/swagger");
        logger.info("OpenAPI JSON at https://localhost:" + Constants.SECURE_PORT + "/openapi");
        logger.info("Swagger docs at http://localhost:" + Constants.INSECURE_PORT + "/swagger");
        logger.info("OpenAPI JSON at http://localhost:" + Constants.INSECURE_PORT + "/openapi");
    }

    private static void printAsciiArt() {
        for (String line : Constants.ASCII_ART) {
            System.out.println(line);
        }
    }

    private static void configureHttpServer(JavalinConfig config) {
        logger.debug("Configuring REST OpenAPI...");

        config.registerPlugin(new OpenApiPlugin(pluginConfig -> {
            pluginConfig.withDefinitionConfiguration((version, definition) -> {
                definition.withInfo(info -> {
                    info.setTitle(Constants.API_TITLE);
                    info.setVersion(Constants.API_VERSION);
                    info.setDescription(Constants.API_DESCRIPTION);
                });
            });
        }));
        config.registerPlugin(new SwaggerPlugin());
        config.showJavalinBanner = false;

        SslPlugin sslPlugin = new SslPlugin(conf -> {
            conf.keystoreFromPath(Constants.KEYSTORE_PATH, Constants.KEYSTORE_PASSWORD);
            conf.withTrustConfig(trustConfig -> {
                trustConfig.trustStoreFromPath(Constants.TRUSTSTORE_PATH, Constants.KEYSTORE_PASSWORD);
            });
            conf.secure = true;
            conf.securePort = Constants.SECURE_PORT;
            conf.insecure = true;
            conf.insecurePort = Constants.INSECURE_PORT;
            conf.sniHostCheck = false;
            conf.http2 = true;
        });

        config.registerPlugin(sslPlugin);
    }

    private static HttpClient createHttpClientWithTrustStore() {
        try {
            // Load the trust store that contains the BIP's certificate
            KeyStore trustStore = KeyStore.getInstance("JKS");
            try (FileInputStream fis = new FileInputStream(Constants.TRUSTSTORE_PATH)) {
                trustStore.load(fis, Constants.KEYSTORE_PASSWORD.toCharArray());
            }

            // Create TrustManagerFactory with the trust store
            TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            tmf.init(trustStore);

            // Load client certificate keystore for client authentication
            KeyStore clientKeyStore = KeyStore.getInstance("JKS");
            try (FileInputStream fis = new FileInputStream(Constants.KEYSTORE_PATH)) {
                clientKeyStore.load(fis, Constants.KEYSTORE_PASSWORD.toCharArray());
            }
            KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            kmf.init(clientKeyStore, Constants.KEYSTORE_PASSWORD.toCharArray());

            // Create SSL context with both trust managers and key managers
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(kmf.getKeyManagers(), tmf.getTrustManagers(), new SecureRandom());

            // Create HttpClient with the SSL context
            return HttpClient.newBuilder()
                    .sslContext(sslContext)
                    .build();
        } catch (Exception e) {
            logger.error("Failed to create HttpClient with SSL configuration, falling back to default", e);
            return HttpClient.newHttpClient();
        }
    }
}
