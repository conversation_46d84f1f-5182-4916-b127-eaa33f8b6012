package fr.enedis.i2r.infra.metrics;

import java.io.IOException;
import java.nio.file.DirectoryIteratorException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.gson.Gson;

import fr.enedis.i2r.comsi.metrics.Metric;
import fr.enedis.i2r.comsi.metrics.MetricsSet;
import fr.enedis.i2r.comsi.metrics.MetricsSourcePort;

public class TelegrafJsonlMetricsSourceAdapter implements MetricsSourcePort {

    private static final Logger logger = LoggerFactory.getLogger(TelegrafJsonlMetricsSourceAdapter.class);

    public static final String DEFAULT_METRICS_FOLDER_PATH = "/var/lib/i2r/metrics";

    private final Path metricsFolderPath;

    private HashMap<MetricsSet, Path> metricFileMap = new HashMap<>();

    public TelegrafJsonlMetricsSourceAdapter(String metricsFolderPath) {
        this.metricsFolderPath = Path.of(metricsFolderPath);
    }

    /**
     * Pour filtrer les fichiers avant de les charger en mémoire.
     */
    DirectoryStream.Filter<Path> filter = entry -> {
        String fileName = entry.getFileName().toString();
        return fileName.endsWith(".jsonl") && !fileName.equals("metrics.jsonl");
    };

    @Override
    public List<MetricsSet> getAvailableMetrics() {
        metricFileMap = new HashMap<>();

        try (DirectoryStream<Path> metricsFiles = Files.newDirectoryStream(metricsFolderPath, filter)) {
            for(Path metricsFile: metricsFiles) {
                MetricsSet metricsSet = parseMetricsFile(metricsFile);
                if (!metricsSet.isEmpty()) {
                    metricFileMap.put(metricsSet, metricsFile);
                }
            }

        } catch (IOException | DirectoryIteratorException e) {
            logger.error("Erreur de récupération des fichiers de métriques", e);
        }

        return new ArrayList<>(metricFileMap.keySet());
    }

    private MetricsSet parseMetricsFile(Path jsonlFile) throws IOException {
        try (Stream<String> rawMetricLines = Files.lines(jsonlFile)) {
            List<Metric> metrics = rawMetricLines
                .map(this::parseRawMetric)
                .flatMap(Optional::stream)
                .map(this::parseMetric)
                .flatMap(Optional::stream)
                .toList();

            return new MetricsSet(metrics);
        }
    }

    private Optional<RawJsonMetric> parseRawMetric(String line) {
        var gson = new Gson();

        try {
            return Optional.of(gson.fromJson(line, RawJsonMetric.class));
        } catch(Throwable e) {
            logger.error("erreur lors du parse json de la ligne de métriques \"{}\"", line, e);
            return Optional.empty();
        }
    }

    private Optional<Metric> parseMetric(RawJsonMetric rawJsonMetric) {
        var metricName = rawJsonMetric.name();

        return switch (metricName) {
            case "serving-cell" -> Optional.of(MetricsParser.parseServingCell(rawJsonMetric));
            default -> {
                logger.warn("métrique non prise en charge : \"{}\"", metricName);
                yield Optional.empty();
            }
        };
    }

    @Override
    public void removeMetrics(MetricsSet metricsSet) {
        Optional<Path> correspondingMetricFile = Optional.ofNullable(metricFileMap.get(metricsSet));

        correspondingMetricFile.ifPresentOrElse(metricFile -> {
            try {
                Files.delete(metricFile);
            } catch(Throwable t) {
                logger.error("erreur lors de la suppression du fichier de métriques", t);
            } finally {
                metricFileMap.remove(metricsSet);
            }

        }, () -> logger.warn("demande de suppression d'un fichier de métriques introuvable"));
    }
}
