package fr.enedis.i2r.infra.timesync;

import static fr.enedis.i2r.system.shell.ShellCommand.CHRONY_TRACKING;

import java.io.BufferedReader;
import java.io.IOException;
import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.system.ports.ShellExecutorPort;
import fr.enedis.i2r.system.ports.TimeSyncPort;
import fr.enedis.i2r.system.ports.TimeSyncStatus;

public class ChronyTimeSyncAdapter implements TimeSyncPort {

    private static final Logger logger = LoggerFactory.getLogger(ChronyTimeSyncAdapter.class);

    private final ShellExecutorPort shellExecutor;

    public ChronyTimeSyncAdapter(ShellExecutorPort shellExecutor) {
        this.shellExecutor = shellExecutor;
    }

    @Override
    public boolean isTimeSynchronized(Duration threshold) {
        try {
            TimeSyncStatus status = parseChronyOutput();

            if (!status.isSynchronized()) {
                return false;
            }

            return status.lastOffset()
                .map(offset -> Math.abs(offset.toSeconds()) < threshold.toSeconds())
                .orElse(false);

        } catch (Exception e) {
            logger.error("Error checking time synchronization with Chrony: {}", e.getMessage(), e);
            return false;
        }
    }

    public TimeSyncStatus parseChronyOutput() {
        try (BufferedReader reader = shellExecutor.execute(CHRONY_TRACKING)) {
            String line;
            Duration lastOffset = null;
            boolean isSynchronized = true;
            String statusMessage = "Normal";
            StringBuilder additionalInfo = new StringBuilder();

            while ((line = reader.readLine()) != null) {
                additionalInfo.append(line).append("\n");

                if (line.startsWith("Last offset")) {
                    try {
                        // Parse "Last offset     : +0.000123456 seconds"
                        String offsetStr = line.split(":")[1].split("seconds")[0].trim();
                        // Remove leading + if present
                        if (offsetStr.startsWith("+")) {
                            offsetStr = offsetStr.substring(1);
                        }
                        double offsetSeconds = Double.parseDouble(offsetStr);
                        lastOffset = Duration.ofNanos((long) (offsetSeconds * 1_000_000_000));
                    } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
                        logger.warn("Could not parse offset from line: {}", line);
                    }
                } else if (line.startsWith("Leap status")) {
                    // Chrony uses british english "synchronised" instead of "synchronized"
                    if (line.contains("Not synchronised")) {
                        isSynchronized = false;
                        statusMessage = "Not synchronised";
                    }
                }
            }

            if (lastOffset != null && isSynchronized) {
                return TimeSyncStatus.withDetails(
                    true,
                    lastOffset,
                    statusMessage,
                    additionalInfo.toString().trim()
                );
            } else if (!isSynchronized) {
                return TimeSyncStatus.withDetails(
                    false,
                    lastOffset,
                    statusMessage,
                    additionalInfo.toString().trim()
                );
            } else {
                return TimeSyncStatus.notSynchronized("Could not determine synchronization status");
            }
        } catch (IOException e) {
            logger.error("Error executing chrony tracking command: {}", e.getMessage(), e);
            return TimeSyncStatus.notSynchronized("Error executing chrony command: " + e.getMessage());
        }
    }
}
