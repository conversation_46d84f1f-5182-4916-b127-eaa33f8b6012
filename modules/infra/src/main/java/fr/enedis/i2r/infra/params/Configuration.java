package fr.enedis.i2r.infra.params;

import java.util.HashMap;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.errors.InvalidParameterException;
import fr.enedis.i2r.comsi.errors.RequiredParameterMissingException;


public record Configuration(HashMap<String, String> parameters) {
    private static final Logger logger = LoggerFactory.getLogger(Configuration.class);

    public Optional<String> get(String key) {
        return Optional.ofNullable(parameters.get(key));
    }

    public <T> T parse(RequiredParameter<T> parameter)
            throws RequiredParameterMissingException, InvalidParameterException {
        String stringValue = get(parameter.primaryKey()).orElseThrow(RequiredParameterMissingException::new);

        return parameter.parser().parse(stringValue);
    }

    public <T> T parseOrDefault(ParameterWithDefault<T> parameter) {
        try {
            Optional<String> stringValue = get(parameter.primaryKey());
            if (stringValue.isEmpty()) {
                return parameter.defaultValue();
            }

            return parameter.parser().parse(stringValue.get());
        } catch (Exception e) {
            logger.error("Paramètre en base incohérent (%s). %s", parameter.defaultValue().getClass().toString(), e);
            return parameter.defaultValue();
        }
    }
}
