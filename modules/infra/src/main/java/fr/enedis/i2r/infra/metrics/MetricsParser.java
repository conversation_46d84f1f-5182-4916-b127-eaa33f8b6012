package fr.enedis.i2r.infra.metrics;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;

import fr.enedis.i2r.comsi.metrics.ServingCellMetrics;

public interface MetricsParser {
    public static ServingCellMetrics parseServingCell(RawJsonMetric rawJsonMetric) {
        var fields = rawJsonMetric.fields();
        Integer earfcn = Integer.parseInt(fields.get("earfcn"));
        Integer mcc = Integer.parseInt(fields.get("mcc"));
        Integer mnc = Integer.parseInt(fields.get("mnc"));
        Integer pci = Integer.parseInt(fields.get("pci"));
        String rat = fields.get("rat");
        Integer rsrp = Integer.parseInt(fields.get("rsrp"));
        Integer rsrq = Integer.parseInt(fields.get("rsrq"));
        Integer rssi = Integer.parseInt(fields.get("rssi"));
        Integer sinr = Integer.parseInt(fields.get("sinr"));
        Integer tac = Integer.parseInt(fields.get("tac"));
        ZonedDateTime emission = Instant.ofEpochSecond(rawJsonMetric.timestamp()).atZone(ZoneId.systemDefault());

        return new ServingCellMetrics(
            earfcn, emission, mcc, mnc, rsrp, rsrq, rssi, sinr, pci, tac, rat
        );
    }
}
