package fr.enedis.i2r.infra.web.controllers;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.infra.timesync.ChronyTimeSyncAdapter;
import fr.enedis.i2r.infra.web.RestEndpoints;
import fr.enedis.i2r.system.ports.ShellExecutorPort;
import fr.enedis.i2r.system.ports.TimeSyncPort;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;
import io.javalin.openapi.HttpMethod;
import io.javalin.openapi.OpenApi;
import io.javalin.openapi.OpenApiContent;
import io.javalin.openapi.OpenApiResponse;

public class MonitoringController {

    private static final Logger logger = LoggerFactory.getLogger(MonitoringController.class);

    private ComSiConfiguration configuration;
    private ShellExecutorPort shellExecutor;

    public MonitoringController(ComSiConfiguration configuration, ShellExecutorPort shellExecutor) {
        this.configuration = configuration;
        this.shellExecutor = shellExecutor;
    }

    @OpenApi(
        summary = "Vérifie si le temps du boitier est synchronisé ou non",
        operationId = "getTimeSync",
        path = RestEndpoints.API_ROOT + RestEndpoints.TIME_SYNC,
        methods = HttpMethod.GET,
        tags = { "Temps" },
        responses = {
            @OpenApiResponse(
                status = "200",
                description = "Retourne 'true' si synchronisé ou 'false' sinon",
                content = {
                    @OpenApiContent(from = Boolean.class)
                }
            )
        }
    )
    public void getTimeSync(Context ctx) {
        TimeSyncPort timeSyncPort = new ChronyTimeSyncAdapter(shellExecutor);
        boolean isTimeSynchronized = timeSyncPort.isTimeSynchronized(configuration.timeDesynchronizationThreshold());

        logger.info("Time is synchronized: {}", isTimeSynchronized);

        ctx.status(HttpStatus.OK).result(String.valueOf(isTimeSynchronized));
    }
}
