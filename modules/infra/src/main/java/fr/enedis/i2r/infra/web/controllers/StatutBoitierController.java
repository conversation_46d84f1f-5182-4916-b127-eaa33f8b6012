package fr.enedis.i2r.infra.web.controllers;

import fr.enedis.i2r.comsi.errors.rest.InvalidRequestBodyException;
import fr.enedis.i2r.comsi.errors.rest.InvalidRequestHeadersException;
import fr.enedis.i2r.comsi.requests.OrdreActivationBoitier;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.comsi.status.BipStatusManager;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;

public class StatutBoitierController {

    private BipStatusManager bipStatusManager;

    public StatutBoitierController(BipStatusManager bipStatusManager) {
        this.bipStatusManager = bipStatusManager;
    }

    public void changeStatus(Context ctx)
        throws InvalidRequestBodyException, InvalidRequestHeadersException {

        OrdreActivationBoitier ordre = ctx.bodyAsClass(OrdreActivationBoitier.class);
        ordre.erdfApiVersion = ctx.header("X-ERDF-API-VERSION");
        ordre.idms = ctx.header("x-idms");
        ordre.erdfHash = ctx.header("X-ERDF-HASH");

        BipStatus requiredBipStatus = BipStatus.fromStatusCode(ordre.state).orElseThrow(
                () -> new InvalidRequestBodyException(String.format("status du bip invalide : %d", ordre.state)));

        bipStatusManager.changeBipStatus(requiredBipStatus, ordre.idms, ordre.erdfHash);

        ctx.status(HttpStatus.OK);
    }
}
