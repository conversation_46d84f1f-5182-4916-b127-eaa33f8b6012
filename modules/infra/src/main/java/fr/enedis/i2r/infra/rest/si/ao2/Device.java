package fr.enedis.i2r.infra.rest.si.ao2;

import java.util.ArrayList;
import java.util.List;

public class Device extends Base {

    private List<Base> objects = new ArrayList<>();

    public Device() {
        this.setClazz("Container");
        this.setName("device");
    }

    public List<Base> getObjects() {
        return this.objects;
    }

    public int getNb() {
        return this.objects.size();
    }

    public void addObject(Base object) {
        this.objects.add(object);
    }
}
