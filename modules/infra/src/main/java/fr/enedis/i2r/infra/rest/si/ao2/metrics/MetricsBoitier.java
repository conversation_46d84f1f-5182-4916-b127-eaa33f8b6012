package fr.enedis.i2r.infra.rest.si.ao2.metrics;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import fr.enedis.i2r.comsi.metrics.Metric;
import fr.enedis.i2r.comsi.metrics.MetricsSet;
import fr.enedis.i2r.comsi.metrics.ServingCellMetrics;
import fr.enedis.i2r.infra.rest.si.ao2.Base;

public class MetricsBoitier extends Base {

    private List<Base> objects = new ArrayList<>();
    private int nb;

    public MetricsBoitier() {
        this.setName("cfg");
        this.setClazz("Container");
        this.setEmitted(Instant.now().toString());
        this.setEntity("LOGDM");
        this.setPub(true);
    }

    public List<Base> getObjects() {
        return this.objects;
    }

    public int getNb() {
        return nb;
    }

    public void addObject(Base object) {
        this.objects.add(object);
        nb = this.objects.size();
    }

    public static MetricsBoitier from(MetricsSet metricsSet) {
        MetricsBoitier metricsBoitier = new MetricsBoitier();

        metricsSet.metrics().stream()
            .map(MetricsBoitier::parseMetric)
            .flatMap(Optional::stream)
            .forEach(metricsBoitier::addObject);

        return metricsBoitier;
    }

    private static Optional<Base> parseMetric(Metric metric) {
        return switch(metric) {
            case ServingCellMetrics servingCell -> Optional.of(StatCellServing.from(servingCell));
            default -> Optional.empty();
        };
    }

}
