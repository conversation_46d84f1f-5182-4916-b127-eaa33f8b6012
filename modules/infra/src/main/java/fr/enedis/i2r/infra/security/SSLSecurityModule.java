package fr.enedis.i2r.infra.security;

import java.io.FileInputStream;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.infra.security.errors.SSLSecurityModuleException;
import io.javalin.community.ssl.SslPlugin;

public class SSLSecurityModule {

    // TODO: Replace keystore with PKCS11 when SecureElement is available
    private static final String KEYSTORE_PATH = "/var/lib/i2r/certificate.jks";
    private static final String KEYSTORE_PASSWORD = "password";

    private static final String TRUSTSTORE_PATH = "/var/lib/i2r/truststore.jks";
    private static final String TRUSTSTORE_PASSWORD = "password";

    private static final Logger logger = LoggerFactory.getLogger(SSLSecurityModule.class);

    /**
     * SSLContext used for making HTTPS requests (client)
     */
    private SSLContext sslContext;

    /**
     * SslConfig use by Javalin to host a secure web server.
     */
    private SslPlugin sslPlugin;

    public SSLSecurityModule() throws SSLSecurityModuleException {
        // TODO : Replace createInsecureSSLContext with createSecureSSLContext
        // when the enedis certificate is available on the linux image
        try {
            sslContext = createInsecureSSLContextFromKeyStore(KEYSTORE_PATH, KEYSTORE_PASSWORD);
            sslPlugin = createSslPlugin(KEYSTORE_PATH, KEYSTORE_PASSWORD, TRUSTSTORE_PATH, TRUSTSTORE_PASSWORD);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new SSLSecurityModuleException("Impossible de créer le SSLContext.");
        }
    }

    public SSLContext getSslContext() {
        return sslContext;
    }

    public SslPlugin getSslPlugin() {
        return this.sslPlugin;
    }

    /**
     * This creates an insecure SSL Context using a keystore which disables server
     * certificate
     * verification (like curl -k)
     */
    private static SSLContext createInsecureSSLContextFromKeyStore(String keystorePath, String keystorePassword)
            throws KeyStoreException, IOException, NoSuchAlgorithmException,
            CertificateException, UnrecoverableKeyException, KeyManagementException {

        KeyStore keyStore = KeyStore.getInstance("JKS");
        try (FileInputStream is = new FileInputStream(keystorePath)) {
            keyStore.load(is, keystorePassword.toCharArray());
        }

        KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
        kmf.init(keyStore, keystorePassword.toCharArray());

        TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }

                    public void checkClientTrusted(java.security.cert.X509Certificate[] certs, String authType) {
                    }

                    public void checkServerTrusted(java.security.cert.X509Certificate[] certs, String authType) {
                    }
                }
        };

        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(kmf.getKeyManagers(), trustAllCerts, new SecureRandom());
        return sslContext;
    }

    private static SslPlugin createSslPlugin(String keystorePath, String keystorePassword, String truststorePath, String truststorePassword) {

        SslPlugin plugin = new SslPlugin(conf -> {
            // Server certificate and private key
            // Set certificates
            conf.keystoreFromPath(keystorePath, keystorePassword);

            // Set truststore (AC)
            conf.withTrustConfig(trustConfig -> {
                trustConfig.trustStoreFromPath(truststorePath, truststorePassword);
            });
            conf.securePort = 443;
            conf.insecurePort = 8081;
            conf.sniHostCheck = false;
        });

        return plugin;
    }
}
