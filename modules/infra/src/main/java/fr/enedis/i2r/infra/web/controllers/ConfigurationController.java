package fr.enedis.i2r.infra.web.controllers;

import java.time.Instant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.ConfigurationBoitier;
import fr.enedis.i2r.comsi.errors.rest.InvalidQueryParamsException;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.ModemManagerPort;
import fr.enedis.i2r.comsi.ports.SecurityModulePort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;
import fr.enedis.i2r.infra.web.RestEndpoints;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;
import io.javalin.openapi.HttpMethod;
import io.javalin.openapi.OpenApi;
import io.javalin.openapi.OpenApiContent;
import io.javalin.openapi.OpenApiParam;
import io.javalin.openapi.OpenApiResponse;

public class ConfigurationController {

    private static final Logger logger = LoggerFactory.getLogger(ConfigurationController.class);

    private ComSiConfiguration comSiConfiguration;
    private ComsiParametersPort parametersPort;
    private BoardManagerPort boardManagerPort;
    private SecurityModulePort moduleSecuritePort;
    private ModemManagerPort modemManagerPort;
    private SiClientPort siClientPort;

    public ConfigurationController(ComSiConfiguration comSiConfiguration,
            ComsiParametersPort parametersPort,
            BoardManagerPort boardManagerPort,
            SecurityModulePort moduleSecuritePort,
            ModemManagerPort modemManagerPort,
            SiClientPort siClientPort) {
        this.comSiConfiguration = comSiConfiguration;
        this.parametersPort = parametersPort;
        this.boardManagerPort = boardManagerPort;
        this.moduleSecuritePort = moduleSecuritePort;
        this.modemManagerPort = modemManagerPort;
        this.siClientPort = siClientPort;
    }

    @OpenApi(
        summary = "Récupère la configuration BIP actuelle",
        operationId = "getConfiguration",
        path = RestEndpoints.DB_CFG,
        methods = HttpMethod.GET,
        tags = { "Configuration" },
        queryParams = {
            @OpenApiParam(
                name = "depth",
                type = Integer.class,
                required = false,
                description = "Profondeur de la configuration (par défaut: 99)"
            )
        },
        responses = {
            @OpenApiResponse(
                status = "200",
                description = "Configuration BIP récupérée avec succès",
                content = { @OpenApiContent(from = ConfigurationBoitier.class) }
            ),
            @OpenApiResponse(
                status = "500",
                description = "Erreur lors de la récupération de la configuration",
                content = { @OpenApiContent(from = String.class) }
            )
        }
    )
    public void getConfiguration(Context ctx) throws Exception {
        try {
            String depthParam = ctx.queryParam("depth");
            int depth = depthParam != null ? Integer.parseInt(depthParam) : 99;
            // TODO Add depth logic: I2R-630

            logger.info("Récupération de la configuration BIP avec profondeur: {}", depth);

            ConfigurationBoitier config = ConfigurationBoitier.from(
                    comSiConfiguration,
                    Instant.now(), // TODO stubbing dates for now, replace by real values
                    parametersPort.getConfigurationHash(),
                    boardManagerPort.getAds(),
                    moduleSecuritePort.getIdms(),
                    modemManagerPort.getIccid()
                );

            // Send config asynchronously
            siClientPort.sendConfigurationBoitier(config);

            ctx.status(HttpStatus.OK);

        } catch (NumberFormatException e) {
            throw new InvalidQueryParamsException("Paramètre depth invalide: doit être un nombre entier");
        }
    }
}
