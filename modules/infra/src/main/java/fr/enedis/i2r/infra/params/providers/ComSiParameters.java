package fr.enedis.i2r.infra.params.providers;

import java.time.Duration;
import java.time.LocalTime;
import java.util.Optional;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.Datacenter;
import fr.enedis.i2r.comsi.IpAddress;
import fr.enedis.i2r.comsi.errors.InvalidParameterException;
import fr.enedis.i2r.comsi.errors.RequiredParameterMissingException;
import fr.enedis.i2r.comsi.params.BipParameter;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.infra.params.Configuration;
import fr.enedis.i2r.infra.params.ParameterWithDefault;
import fr.enedis.i2r.infra.params.RequiredParameter;

public class ComSiParameters {
    public static final ParameterWithDefault<Duration> PERIOD_BETWEEN_PINGS = new ParameterWithDefault<>(
            "i2r.si.ping.period-ms",
            (valeur) -> {
                long millisecondes = Long.parseLong(valeur);
                return Duration.ofMillis(millisecondes);
            },
            ComSiConfiguration.DEFAULT_PERIOD_BETWEEN_PINGS);

    public static final ParameterWithDefault<Duration> MAXIMUM_RANDOM_PERIOD_BETWEEN_PINGS = new ParameterWithDefault<>(
            "i2r.si.ping.maximum-random-delay-ms",
            (valeur) -> {
                long millisecondes = Long.parseLong(valeur);
                return Duration.ofMillis(millisecondes);
            },
            ComSiConfiguration.DEFAULT_MAXIMUM_RANDOM_PERIOD_BETWEEN_PINGS);

    public static final ParameterWithDefault<Duration> PING_TIMEOUT = new ParameterWithDefault<>(
            "i2r.si.ping.timeout",
            (valeur) -> {
                long millisecondes = Long.parseLong(valeur);
                return Duration.ofMillis(millisecondes);
            },
            ComSiConfiguration.DEFAULT_PING_TIMEOUT);

    public static final ParameterWithDefault<IpAddress> NOE_IPV4 = new ParameterWithDefault<>(
            "i2r.si.ip.noe",
            (valeur) -> new IpAddress(valeur),
            ComSiConfiguration.DEFAULT_NOE_IPV4);

    public static final ParameterWithDefault<IpAddress> PACY_IPV4 = new ParameterWithDefault<>(
            "i2r.si.ip.pacy",
            (valeur) -> new IpAddress(valeur),
            ComSiConfiguration.DEFAULT_PACY_IPV4);

    public static final ParameterWithDefault<Integer> FAILED_PINGS_LIMIT_BEFORE_MODEM_RESET = new ParameterWithDefault<>(
            "i2r.si.ping.consecutive-failed-pings-threshold-before-wan-reset",
            (valeur) -> Integer.parseInt(valeur),
            ComSiConfiguration.DEFAULT_FAILED_PINGS_LIMIT_BEFORE_MODEM_RESET);

    public static final ParameterWithDefault<Integer> MODEM_RESETS_LIMIT_BEFORE_AO3_REBOOT = new ParameterWithDefault<>(
            "i2r.si.ping.reboot.threshold",
            (valeur) -> Integer.parseInt(valeur),
            ComSiConfiguration.DEFAULT_MODEM_RESETS_LIMIT_BEFORE_AO3_REBOOT);

    public static final ParameterWithDefault<Integer> NETMASK_SIZE = new ParameterWithDefault<>(
            "i2r.si.netmask.size",
            (valeur) -> Integer.parseInt(valeur),
            ComSiConfiguration.DEFAULT_NETMASK_SIZE);

    public static final ParameterWithDefault<Integer> PING_RETRY_LIMIT = new ParameterWithDefault<>(
            "i2r.si.ip.retry.limit",
            (valeur) -> Integer.parseInt(valeur),
            ComSiConfiguration.DEFAULT_PING_RETRY_LIMIT);

    public static final ParameterWithDefault<Duration> TIME_DESYNCHRONIZATION_THRESHOLD = new ParameterWithDefault<>(
            "i2r.si.time.sync.threshold",
            (valeur) -> {
                long seconds = Long.parseLong(valeur);
                return Duration.ofMillis(seconds);
            },
            ComSiConfiguration.DEFAULT_TIME_DESYNCHRONIZATION_THRESHOLD);

    public static final ParameterWithDefault<BipStatus> BIP_STATUS = new ParameterWithDefault<>(
            BipParameter.BipState.parameterKey,
            (valeur) -> {
                int numericValue = Integer.parseInt(valeur);
                return BipStatus.fromStatusCode(numericValue).get();
            },
            ComSiConfiguration.DEFAULT_BIP_STATUS);

    public static final ParameterWithDefault<LocalTime> METRICS_SEND_INTERVAL_START = new ParameterWithDefault<>(
        "i2r.metrics.send-interval.start-time",
        LocalTime::parse,
        ComSiConfiguration.DEFAULT_METRICS_SEND_INTERVAL_START);

    public static final ParameterWithDefault<Duration> METRICS_MAXIMUM_SEND_INTERVAL_END = new ParameterWithDefault<>(
        "i2r.metrics.send-interval.maximum-length-minutes",
        (valeur) -> {
            long mins = Long.parseLong(valeur);
            return Duration.ofMinutes(mins);
        },
        ComSiConfiguration.DEFAULT_METRICS_SEND_INTERVAL_MAXIMUM_LENGTH);

    public static final ParameterWithDefault<Optional<LocalTime>> METRICS_SEND_TIME = new ParameterWithDefault<>(
        "i2r.metrics.send-time",
        (valeur) -> {
            return Optional.of(LocalTime.parse(valeur));
        },
        ComSiConfiguration.DEFAULT_METRICS_SEND_TIME);

    public static final RequiredParameter<Datacenter> PRIMARY_DATACENTER = new RequiredParameter<>(
        "i2r.si.primary-datacenter",
        (valeur) -> {
            return switch (valeur.toLowerCase()) {
                case "pacy" -> Datacenter.PACY;
                case "noe" -> Datacenter.NOE;
                case String other -> {
                    throw new InvalidParameterException(other);
                }
            };
        });

    public static ComSiConfiguration getComSiConfiguration(Configuration configuration)
            throws InvalidParameterException, RequiredParameterMissingException {

        var primaryDatacenter = configuration.parse(PRIMARY_DATACENTER);

        try {
            return new ComSiConfiguration(
                    configuration.parseOrDefault(PERIOD_BETWEEN_PINGS),
                    configuration.parseOrDefault(MAXIMUM_RANDOM_PERIOD_BETWEEN_PINGS),
                    configuration.parseOrDefault(PING_TIMEOUT),
                    configuration.parseOrDefault(NOE_IPV4),
                    configuration.parseOrDefault(PACY_IPV4),
                    configuration.parseOrDefault(FAILED_PINGS_LIMIT_BEFORE_MODEM_RESET),
                    configuration.parseOrDefault(MODEM_RESETS_LIMIT_BEFORE_AO3_REBOOT),
                    configuration.parseOrDefault(NETMASK_SIZE),
                    configuration.parseOrDefault(PING_RETRY_LIMIT),
                    configuration.parseOrDefault(TIME_DESYNCHRONIZATION_THRESHOLD),
                    primaryDatacenter,
                    configuration.parseOrDefault(BIP_STATUS),
                    configuration.parseOrDefault(METRICS_SEND_INTERVAL_START),
                    configuration.parseOrDefault(METRICS_MAXIMUM_SEND_INTERVAL_END),
                    configuration.parseOrDefault(METRICS_SEND_TIME)
                );

        } catch (Exception e) {
            throw new IllegalArgumentException("Rassemblement de la configuration de ComSI", e);
        }
    }
}
