package fr.enedis.i2r.infra.web;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.ModemManagerPort;
import fr.enedis.i2r.comsi.ports.SecurityModulePort;
import fr.enedis.i2r.comsi.ports.WebServerPort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;
import fr.enedis.i2r.comsi.status.BipStatusManager;
import fr.enedis.i2r.system.LoggingLoader;
import fr.enedis.i2r.system.ports.ShellExecutorPort;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;
import io.javalin.community.ssl.SslPlugin;

public class WebServerAdapter implements WebServerPort {

    private final JavalinHttpServer javalinServer;

    public WebServerAdapter(BipStatusManager bipStatusManager, ComSiConfiguration comSiConfiguration,
            ShellExecutorPort shellExecutorPort, LoggingLoader loggingLoader, SslPlugin sslPlugin,
            ComsiParametersPort parametersPort, BoardManagerPort boardManagerPort,
            SecurityModulePort moduleSecuritePort, ModemManagerPort modemManagerPort, SiClientPort siClientPort,
            ThreadWatchdog threadWatchdog) {

        this.javalinServer = new JavalinHttpServer(
            bipStatusManager, comSiConfiguration, shellExecutorPort, loggingLoader, sslPlugin,
            parametersPort, boardManagerPort, moduleSecuritePort, modemManagerPort, siClientPort, threadWatchdog
        );
    }

    @Override
    public void start() {
        javalinServer.start();
    }

    @Override
    public void stop() {
        javalinServer.stop();
    }

    @Override
    public boolean isRunning() {
        return javalinServer.isRunning();
    }

    @Override
    public void run() {
        javalinServer.run();
    }
}
