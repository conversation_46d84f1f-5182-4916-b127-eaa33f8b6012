package fr.enedis.i2r.infra;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.ArgumentCaptor;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.metrics.MetricsSender;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.infra.metrics.TelegrafJsonlMetricsSourceAdapter;
import fr.enedis.i2r.infra.rest.CustomComSiConfiguration;
import fr.enedis.i2r.infra.rest.HttpClient;
import fr.enedis.i2r.infra.rest.HttpRequest;
import fr.enedis.i2r.infra.rest.HttpResponse;
import fr.enedis.i2r.infra.rest.SiClientHttpAdapter;
import fr.enedis.i2r.infra.rest.si.ao2.metrics.MetricsBoitier;
import fr.enedis.i2r.infra.rest.si.error.IComBadRequestException;
import io.javalin.http.HttpStatus;

public class MetricsSendingTest {

    @TempDir
    Path metricsTempFolder;

    void setupTestFiles(List<String> filesToInclude) throws IOException {
        for (String fileName : filesToInclude) {
            // Chemin source dans resources
            Path source = Paths.get("src/test/resources/metrics-test-files", fileName);
            // Chemin cible dans le dossier temporaire
            Path target = metricsTempFolder.resolve(fileName);

            // Copie du fichier
            Files.copy(source, target);

            // Vérification que le fichier a bien été copié
            assertTrue(Files.exists(target), "Le fichier " + fileName + " n'a pas été copié.");
        }
    }

    @Test
    void les_metriques_connues_sont_envoyees_au_si_et_supprimees_en_cas_de_succes() throws IOException, IComBadRequestException {
        setupTestFiles(List.of("two-valid-metrics.jsonl"));

        HttpClient httpClient = mock(HttpClient.class);

        var successfulResponse = new HttpResponse();
        successfulResponse.setStatus(HttpStatus.OK);
        when(httpClient.retryRequest(any())).thenReturn(successfulResponse);

        ComSiConfiguration conf = new CustomComSiConfiguration().build();

        var metricsSource = new TelegrafJsonlMetricsSourceAdapter(metricsTempFolder.toString());
        ComsiParametersPort mockParametersPort = mockParametersPort();
        SiClientHttpAdapter siClientHttpAdapter = new SiClientHttpAdapter(conf, httpClient, mockParametersPort, "ADS", "IDMS");
        MetricsSender metricsSender = new MetricsSender(new CustomComSiConfiguration().build(), mockParametersPort, siClientHttpAdapter, metricsSource);

        metricsSender.sendMetrics();

        ArgumentCaptor<HttpRequest> captor = ArgumentCaptor.forClass(HttpRequest.class);
        verify(httpClient).retryRequest(captor.capture());
        HttpRequest requeteEnvoyee = captor.getValue();

        MetricsBoitier sentMetrics = (MetricsBoitier) requeteEnvoyee.payload();

        assertThat(sentMetrics.getObjects()).hasSize(2);
        assertThat(Files.exists(metricsTempFolder.resolve("two-valid-metrics.jsonl"))).isFalse();
    }

    @Test
    void les_fichiers_de_metriques_non_envoyes_ne_sont_pas_supprimes() throws IOException, IComBadRequestException {
        setupTestFiles(List.of("two-valid-metrics.jsonl", "four-valid-metrics.jsonl"));

        HttpClient httpClient = mock(HttpClient.class);

        var failedResponse = new HttpResponse();
        failedResponse.setStatus(HttpStatus.BAD_REQUEST);
        when(httpClient.retryRequest(any())).thenReturn(failedResponse);

        ComSiConfiguration conf = new CustomComSiConfiguration().build();

        var metricsSource = new TelegrafJsonlMetricsSourceAdapter(metricsTempFolder.toString());
        ComsiParametersPort mockParametersPort = mockParametersPort();
        SiClientHttpAdapter siClientHttpAdapter = new SiClientHttpAdapter(conf, httpClient, mockParametersPort, "ADS", "IDMS");
        MetricsSender metricsSender = new MetricsSender(new CustomComSiConfiguration().build(), mockParametersPort, siClientHttpAdapter, metricsSource);

        metricsSender.sendMetrics();

        verify(httpClient, times(4)).retryRequest(any());

        assertThat(Files.exists(metricsTempFolder.resolve("four-valid-metrics.jsonl"))).isTrue();
        assertThat(Files.exists(metricsTempFolder.resolve("two-valid-metrics.jsonl"))).isTrue();
    }

    @Test
    void seuls_les_fichiers_ayant_ete_envoyes_sont_supprimes() throws IOException, IComBadRequestException {
        setupTestFiles(List.of("two-valid-metrics.jsonl", "four-valid-metrics.jsonl"));

        HttpClient httpClient = mock(HttpClient.class);

        var successfulResponse = new HttpResponse();
        successfulResponse.setStatus(HttpStatus.NO_CONTENT);
        var failedResponse = new HttpResponse();
        failedResponse.setStatus(HttpStatus.BAD_REQUEST);

        // Le premier fichier échouera sur un DC puis l'autre, et l'autre fichier réussira
        when(httpClient.retryRequest(any())).thenReturn(failedResponse, failedResponse, successfulResponse);

        ComSiConfiguration conf = new CustomComSiConfiguration().build();

        var metricsSource = new TelegrafJsonlMetricsSourceAdapter(metricsTempFolder.toString());
        ComsiParametersPort mockParametersPort = mockParametersPort();
        SiClientHttpAdapter siClientHttpAdapter = new SiClientHttpAdapter(conf, httpClient, mockParametersPort, "ADS", "IDMS");
        MetricsSender metricsSender = new MetricsSender(new CustomComSiConfiguration().build(), mockParametersPort, siClientHttpAdapter, metricsSource);

        metricsSender.sendMetrics();

        verify(httpClient, times(3)).retryRequest(any());

        long numberOfMetricFiles = Files.list(metricsTempFolder)
                  .filter(Files::isRegularFile)
                  .count();

        assertThat(numberOfMetricFiles).isEqualTo(1);
    }

    @Test
    void les_lignes_corrompues_n_empechent_pas_les_autres_lignes_d_etre_traitees() throws IOException, IComBadRequestException {
        setupTestFiles(List.of("three-handled-one-corrupted.jsonl"));

        HttpClient httpClient = mock(HttpClient.class);

        var successfulResponse = new HttpResponse();
        successfulResponse.setStatus(HttpStatus.OK);
        when(httpClient.retryRequest(any())).thenReturn(successfulResponse);

        ComSiConfiguration conf = new CustomComSiConfiguration().build();

        var metricsSource = new TelegrafJsonlMetricsSourceAdapter(metricsTempFolder.toString());
        ComsiParametersPort mockParametersPort = mockParametersPort();
        SiClientHttpAdapter siClientHttpAdapter = new SiClientHttpAdapter(conf, httpClient, mockParametersPort, "ADS", "IDMS");
        MetricsSender metricsSender = new MetricsSender(new CustomComSiConfiguration().build(), mockParametersPort, siClientHttpAdapter, metricsSource);

        metricsSender.sendMetrics();

        ArgumentCaptor<HttpRequest> captor = ArgumentCaptor.forClass(HttpRequest.class);
        verify(httpClient).retryRequest(captor.capture());
        HttpRequest requeteEnvoyee = captor.getValue();

        MetricsBoitier sentMetrics = (MetricsBoitier) requeteEnvoyee.payload();

        assertThat(sentMetrics.getObjects()).hasSize(3);
        assertThat(Files.exists(metricsTempFolder.resolve("two-handled-one-corrupted.jsonl"))).isFalse();
    }

    private ComsiParametersPort mockParametersPort() {
        ComsiParametersPort comsiParametersPort = mock(ComsiParametersPort.class);

        when(comsiParametersPort.getConfigurationHash()).thenReturn("HASH");

        return comsiParametersPort;
    }
}
