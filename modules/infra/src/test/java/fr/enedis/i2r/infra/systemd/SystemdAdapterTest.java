package fr.enedis.i2r.infra.systemd;

import static org.assertj.core.api.Assertions.assertThatCode;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;

import org.freedesktop.dbus.connections.impl.DBusConnection;
import org.freedesktop.dbus.exceptions.DBusException;
import org.freedesktop.systemd1.Manager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import fr.enedis.i2r.infra.hal.DbusConnectionManager;

class SystemdAdapterTest {

    private Manager mockManager;

    @BeforeEach
    void setUp() {
        mockManager = mock(Manager.class);
    }

    @Test
    void la_creation_reussit_avec_une_connexion_dbus_valide() throws Exception {
        try (MockedStatic<DbusConnectionManager> mockedConnectionManager = Mockito.mockStatic(DbusConnectionManager.class)) {
            DBusConnection mockConnection = mock(DBusConnection.class);
            mockedConnectionManager.when(DbusConnectionManager::getInstance).thenReturn(mockConnection);

            when(mockConnection.getRemoteObject(
                    "org.freedesktop.systemd1",
                    "/org/freedesktop/systemd1",
                    Manager.class))
                    .thenReturn(mockManager);

            assertThatCode(() -> new SystemdAdapter())
                    .doesNotThrowAnyException();
        }
    }

    @Test
    void la_creation_gere_les_echecs_de_connexion_dbus() throws Exception {
        try (MockedStatic<DbusConnectionManager> mockedConnectionManager = Mockito.mockStatic(DbusConnectionManager.class)) {
            mockedConnectionManager.when(DbusConnectionManager::getInstance)
                    .thenThrow(new DBusException("Test exception"));

            assertThatCode(() -> new SystemdAdapter())
                    .doesNotThrowAnyException();
        }
    }

    @Test
    void un_service_unique_demarre_correctement() throws Exception {
        try (MockedStatic<DbusConnectionManager> mockedConnectionManager = Mockito.mockStatic(DbusConnectionManager.class)) {
            DBusConnection mockConnection = mock(DBusConnection.class);
            mockedConnectionManager.when(DbusConnectionManager::getInstance).thenReturn(mockConnection);

            when(mockConnection.getRemoteObject(
                    "org.freedesktop.systemd1",
                    "/org/freedesktop/systemd1",
                    Manager.class))
                    .thenReturn(mockManager);

            when(mockManager.StartUnit("test.service", "replace")).thenReturn(null);

            SystemdAdapter adapter = new SystemdAdapter();
            List<String> services = Arrays.asList("test.service");

            assertThatCode(() -> adapter.startService(services))
                    .doesNotThrowAnyException();

            verify(mockManager).StartUnit("test.service", "replace");
        }
    }

    @Test
    void plusieurs_services_demarrent_correctement() throws Exception {
        try (MockedStatic<DbusConnectionManager> mockedConnectionManager = Mockito.mockStatic(DbusConnectionManager.class)) {
            DBusConnection mockConnection = mock(DBusConnection.class);
            mockedConnectionManager.when(DbusConnectionManager::getInstance).thenReturn(mockConnection);

            when(mockConnection.getRemoteObject(
                    "org.freedesktop.systemd1",
                    "/org/freedesktop/systemd1",
                    Manager.class))
                    .thenReturn(mockManager);

            when(mockManager.StartUnit("service1.service", "replace")).thenReturn(null);
            when(mockManager.StartUnit("service2.service", "replace")).thenReturn(null);

            SystemdAdapter adapter = new SystemdAdapter();
            List<String> services = Arrays.asList("service1.service", "service2.service");

            assertThatCode(() -> adapter.startService(services))
                    .doesNotThrowAnyException();

            verify(mockManager).StartUnit("service1.service", "replace");
            verify(mockManager).StartUnit("service2.service", "replace");
        }
    }
}
