package fr.enedis.i2r.infra;

import static org.assertj.core.api.Assertions.assertThatThrownBy;

import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ThreadSleeperAdapterTest {

    private ThreadSleeperAdapter adapter;

    @BeforeEach
    void setUp() {
        adapter = new ThreadSleeperAdapter();
    }

    @Test
    void la_duree_d_attente_est_respectee() throws InterruptedException {
        Duration sleepDuration = Duration.ofMillis(10);
        long startTime = System.currentTimeMillis();

        adapter.sleep(sleepDuration);

        long endTime = System.currentTimeMillis();
        long actualSleepTime = endTime - startTime;

        assert actualSleepTime >= sleepDuration.toMillis() - 5;
        assert actualSleepTime <= sleepDuration.toMillis() + 50;
    }

    @Test
    void les_interruptions_de_thread_sont_propagees() {
        Duration sleepDuration = Duration.ofSeconds(1);

        Thread.currentThread().interrupt();

        assertThatThrownBy(() -> adapter.sleep(sleepDuration))
                .isInstanceOf(InterruptedException.class);

        Thread.interrupted();
    }

    @Test
    void les_durees_zero_sont_gerees_correctement() throws InterruptedException {
        Duration zeroDuration = Duration.ZERO;
        long startTime = System.currentTimeMillis();

        adapter.sleep(zeroDuration);

        long endTime = System.currentTimeMillis();
        long actualSleepTime = endTime - startTime;

        assert actualSleepTime < 10;
    }
}
