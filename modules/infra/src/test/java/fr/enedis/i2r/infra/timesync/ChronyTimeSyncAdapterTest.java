package fr.enedis.i2r.infra.timesync;

import static fr.enedis.i2r.system.shell.ShellCommand.CHRONY_TRACKING;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.io.BufferedReader;
import java.io.StringReader;
import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.system.ports.ShellExecutorPort;
import fr.enedis.i2r.system.ports.TimeSyncStatus;

class ChronyTimeSyncAdapterTest {

    public static final Duration THRESHOLD_TIME_DESYNCHRONISATION = Duration.ofSeconds(1);

    private ChronyTimeSyncAdapter adapter;
    private ShellExecutorPort shell;

    @BeforeEach
    public void setup() {
        shell = mock(ShellExecutorPort.class);
        adapter = new ChronyTimeSyncAdapter(shell);
    }

    @Test
    void un_offset_positif_en_dessous_de_la_limite_retourne_true() {
        String text = """
                Last offset     : 0.000123456 seconds
                Leap status     : Normal
                """.stripIndent();
        BufferedReader reader = new BufferedReader(new StringReader(text));
        when(shell.execute(CHRONY_TRACKING)).thenReturn(reader);

        assertThat(adapter.isTimeSynchronized(THRESHOLD_TIME_DESYNCHRONISATION)).isTrue();
    }

    @Test
    void un_offset_negatif_au_dessus_de_la_limite_retourne_false() {
        String text = """
                Last offset     : -2.000004740 seconds
                Leap status     : Normal
                """.stripIndent();
        BufferedReader reader = new BufferedReader(new StringReader(text));
        when(shell.execute(CHRONY_TRACKING)).thenReturn(reader);

        assertThat(adapter.isTimeSynchronized(THRESHOLD_TIME_DESYNCHRONISATION)).isFalse();
    }

    @Test
    void le_statut_not_synchronised_retourne_false() {
        String text = """
                Last offset     : +0.000000000 seconds
                Leap status     : Not synchronised
                """.stripIndent();
        BufferedReader reader = new BufferedReader(new StringReader(text));
        when(shell.execute(CHRONY_TRACKING)).thenReturn(reader);

        assertThat(adapter.isTimeSynchronized(THRESHOLD_TIME_DESYNCHRONISATION)).isFalse();
    }

    @Test
    void la_sortie__de_chrony_est_parsee_correctement() {
        String text = """
                Reference ID    : *********** (***********)
                Stratum         : 3
                Ref time (UTC)  : Thu Oct 01 10:30:45 2025
                System time     : 0.000123456 seconds fast of NTP time
                Last offset     : +0.000123456 seconds
                RMS offset      : 0.000234567 seconds
                Frequency       : 15.123 ppm slow
                Residual freq   : +0.001 ppm
                Skew            : 0.123 ppm
                Root delay      : 0.001234567 seconds
                Root dispersion : 0.002345678 seconds
                Update interval : 64.0 seconds
                Leap status     : Normal
                """.stripIndent();
        BufferedReader reader = new BufferedReader(new StringReader(text));
        when(shell.execute(CHRONY_TRACKING)).thenReturn(reader);

        TimeSyncStatus status = adapter.parseChronyOutput();

        assertThat(status.isSynchronized()).isTrue();
        assertThat(status.lastOffset()).isPresent();
        assertThat(status.lastOffset().get().toNanos()).isEqualTo(123456);
        assertThat(status.lastOffset().get().toMillis()).isEqualTo(0); // Should be 0 since it's less than 1ms
        assertThat(status.statusMessage()).contains("Normal");
        assertThat(status.additionalInfo()).isPresent();
    }

    @Test
    void le_statut_non_synchronise_est_gere() {
        String text = """
                Last offset     : +0.000000000 seconds
                Leap status     : Not synchronised
                """.stripIndent();
        BufferedReader reader = new BufferedReader(new StringReader(text));
        when(shell.execute(CHRONY_TRACKING)).thenReturn(reader);

        TimeSyncStatus status = adapter.parseChronyOutput();

        assertThat(status.isSynchronized()).isFalse();
        assertThat(status.statusMessage()).contains("Not synchronised");
    }
}
