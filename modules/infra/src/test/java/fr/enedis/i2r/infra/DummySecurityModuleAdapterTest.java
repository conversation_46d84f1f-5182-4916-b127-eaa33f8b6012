package fr.enedis.i2r.infra;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.ports.BoardManagerPort;

class DummySecurityModuleAdapterTest {

    private BoardManagerPort mockBoardManagerPort;
    private DummySecurityModuleAdapter adapter;

    @BeforeEach
    void setUp() {
        mockBoardManagerPort = mock(BoardManagerPort.class);
        adapter = new DummySecurityModuleAdapter(mockBoardManagerPort);
    }

    @Test
    void l_idms_est_retourne_depuis_un_ads_valide() throws Exception {
        String validAds = "0625DB00000600";
        String expectedIdms = "475000DE007860000010000001000222";

        when(mockBoardManagerPort.getAds()).thenReturn(validAds);

        String actualIdms = adapter.getIdms();

        assertThat(actualIdms).isEqualTo(expectedIdms);
    }

    @Test
    void l_idms_par_defaut_est_retourne_quand_l_ads_est_invalide() throws Exception {
        String invalidAds = "INVALID_ADS";

        when(mockBoardManagerPort.getAds()).thenReturn(invalidAds);

        String actualIdms = adapter.getIdms();

        assertThat(actualIdms).isEqualTo("IDMSDETEST");
    }

    @Test
    void l_idms_par_defaut_est_retourne_quand_l_ads_est_null() throws Exception {
        when(mockBoardManagerPort.getAds()).thenReturn(null);

        String actualIdms = adapter.getIdms();

        assertThat(actualIdms).isEqualTo("IDMSDETEST");
    }

    @Test
    void la_conversion_ads_vers_idms_retourne_vide_pour_ads_invalide() {
        String invalidAds = "INVALID_ADS";

        Optional<String> result = adapter.getIdmsFromAds(invalidAds);

        assertThat(result).isEmpty();
    }

    @Test
    void la_conversion_ads_vers_idms_fonctionne_pour_ads_valide() {
        String validAds = "0225DB00000225";
        String expectedIdms = "475000DE007860000010000001000354";

        Optional<String> result = adapter.getIdmsFromAds(validAds);

        assertThat(result).isPresent();
        assertThat(result.get()).isEqualTo(expectedIdms);
    }

    @Test
    void le_board_manager_port_est_utilise_correctement() {
        assertThat(adapter).isNotNull();
        when(mockBoardManagerPort.getAds()).thenReturn("TEST_ADS");

        try {
            String result = adapter.getIdms();
            assertThat(result).isEqualTo("IDMSDETEST");
        } catch (Exception e) {
            assertThat(false).isTrue();
        }
    }
}
