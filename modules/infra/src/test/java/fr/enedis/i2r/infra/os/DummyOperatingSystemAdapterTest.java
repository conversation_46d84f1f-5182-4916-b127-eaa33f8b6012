package fr.enedis.i2r.infra.os;

import static org.assertj.core.api.Assertions.assertThatCode;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class DummyOperatingSystemAdapterTest {

    private DummyOperatingSystemAdapter adapter;

    @BeforeEach
    void setUp() {
        adapter = new DummyOperatingSystemAdapter();
    }

    @Test
    void le_redemarrage_du_bip_s_execute_sans_erreur() {
        assertThatCode(() -> adapter.rebootBIP())
                .doesNotThrowAnyException();
    }
}
