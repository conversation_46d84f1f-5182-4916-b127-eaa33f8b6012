package fr.enedis.i2r.infra.params;

import static org.assertj.core.api.Assertions.assertThat;

import java.sql.SQLException;
import java.util.HashMap;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.params.BipParameter;

public class SqliteParamsHasherTest {

    private SqliteParamsHasher hasher;

    @BeforeEach
    void setup() throws SQLException {
        var parametersMap = new HashMap<String, String>();
        parametersMap.put(BipParameter.BipState.parameterKey, "1");
        parametersMap.put(BipParameter.PingPeriodInMs.parameterKey, "5000");
        Configuration configuration = new Configuration(parametersMap);

        hasher = new SqliteParamsHasher(configuration);
    }

    @Test
    void hash_change_lorsque_les_valeurs_sont_differentes() throws SQLException {
        String hash1 = hasher.generateHash();

        var parametersMap = new HashMap<String, String>();
        parametersMap.put(BipParameter.BipState.parameterKey, "2");
        parametersMap.put(BipParameter.PingPeriodInMs.parameterKey, "4000");
        Configuration configuration = new Configuration(parametersMap);

        hasher = new SqliteParamsHasher(configuration);

        String hash2 = hasher.generateHash();

        assertThat(hash1).isNotEqualTo(hash2);
    }

    @Test
    void hash_est_identique_pour_des_valeurs_avec_casses_differentes() throws SQLException {
        String hash1 = hasher.generateHash();

        var parametersMap = new HashMap<String, String>();
        parametersMap.put(BipParameter.BipState.parameterKey.toUpperCase(), "1");
        parametersMap.put(BipParameter.PingPeriodInMs.parameterKey.toUpperCase(), "5000");
        Configuration configuration = new Configuration(parametersMap);

        hasher = new SqliteParamsHasher(configuration);

        String hash2 = hasher.generateHash();

        assertThat(hash1).isEqualTo(hash2);
    }

    @Test
    void hash_est_identique_pour_des_valeurs_identiques_mais_dans_un_ordre_different() throws SQLException {
        String hash1 = hasher.generateHash();

        var parametersMap = new HashMap<String, String>();
        parametersMap.put(BipParameter.PingPeriodInMs.parameterKey, "5000");
        parametersMap.put(BipParameter.BipState.parameterKey, "1");
        Configuration configuration = new Configuration(parametersMap);

        hasher = new SqliteParamsHasher(configuration);

        String hash2 = hasher.generateHash();

        assertThat(hash1).isEqualTo(hash2);
    }

    @Test
    void hash_est_identique_pour_des_valeurs_avec_espaces_supplementaires() throws SQLException {
        String hash1 = hasher.generateHash();

        var parametersMap = new HashMap<String, String>();
        parametersMap.put(BipParameter.BipState.parameterKey, "  1  ");
        parametersMap.put(BipParameter.PingPeriodInMs.parameterKey, "5000  ");
        Configuration configuration = new Configuration(parametersMap);

        hasher = new SqliteParamsHasher(configuration);

        String hash2 = hasher.generateHash();

        assertThat(hash1).isEqualTo(hash2);
    }

    @Test
    void hash_inclut_seulement_les_parametres_watched_de_BipParameter() throws SQLException {
        var parametersMap = new HashMap<String, String>();
        // Ajout d'un paramètre BipParameter watched
        parametersMap.put(BipParameter.BipState.parameterKey, "1");
        // Ajout d'un paramètre qui n'existe pas dans BipParameter
        parametersMap.put("parametre.inexistant", "valeur");
        Configuration configuration = new Configuration(parametersMap);

        SqliteParamsHasher hasher = new SqliteParamsHasher(configuration);
        String hash1 = hasher.generateHash();

        // Hash avec seulement le paramètre BipParameter
        var parametersMap2 = new HashMap<String, String>();
        parametersMap2.put(BipParameter.BipState.parameterKey, "1");
        Configuration configuration2 = new Configuration(parametersMap2);

        SqliteParamsHasher hasher2 = new SqliteParamsHasher(configuration2);
        String hash2 = hasher2.generateHash();

        // Les hash doivent être identiques car seuls les paramètres BipParameter watched sont inclus
        assertThat(hash1).isEqualTo(hash2);
    }

    @Test
    void hash_vide_quand_aucun_parametre_watched_present() throws SQLException {
        var parametersMap = new HashMap<String, String>();
        // Ajout seulement de paramètres qui ne sont pas dans BipParameter
        parametersMap.put("parametre.inexistant1", "valeur1");
        parametersMap.put("parametre.inexistant2", "valeur2");
        Configuration configuration = new Configuration(parametersMap);

        SqliteParamsHasher hasher = new SqliteParamsHasher(configuration);
        String hash = hasher.generateHash();

        // Hash d'une configuration vide
        var parametersMapVide = new HashMap<String, String>();
        Configuration configurationVide = new Configuration(parametersMapVide);
        SqliteParamsHasher hasherVide = new SqliteParamsHasher(configurationVide);
        String hashVide = hasherVide.generateHash();

        // Les hash doivent être identiques car aucun paramètre watched n'est présent
        assertThat(hash).isEqualTo(hashVide);
    }
}
