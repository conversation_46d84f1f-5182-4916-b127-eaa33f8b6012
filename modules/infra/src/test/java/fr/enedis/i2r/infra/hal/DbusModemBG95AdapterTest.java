package fr.enedis.i2r.infra.hal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import org.freedesktop.dbus.connections.impl.DBusConnection;
import org.freedesktop.dbus.exceptions.DBusException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import fr.enedis.hal.ModemManager1;

class DbusModemBG95AdapterTest {

    private ModemManager1 mockModemManager;
    private DbusModemBG95Adapter adapter;

    @BeforeEach
    void setUp() {
        mockModemManager = mock(ModemManager1.class);
    }

    @Test
    void l_iccid_est_recupere_depuis_le_modem_manager() throws Exception {
        String expectedIccid = "89330123456789012345";
        when(mockModemManager.getICCID()).thenReturn(expectedIccid);

        adapter = new DbusModemBG95Adapter(mockModemManager);
        String actualIccid = adapter.getIccid();

        assertThat(actualIccid).isEqualTo(expectedIccid);
    }

    @Test
    void une_exception_est_levee_quand_la_recuperation_iccid_echoue() throws Exception {
        when(mockModemManager.getICCID()).thenThrow(new RuntimeException("Modem error"));

        adapter = new DbusModemBG95Adapter(mockModemManager);

        assertThatThrownBy(() -> adapter.getIccid())
            .isInstanceOf(Exception.class)
            .hasMessage("requête du signal du modem");
    }

    @Test
    void la_connexion_statique_cree_un_adapter_avec_succes() throws DBusException {
        try (MockedStatic<DbusConnectionManager> mockedConnectionManager = mockStatic(DbusConnectionManager.class)) {
            DBusConnection mockConnection = mock(DBusConnection.class);
            mockedConnectionManager.when(DbusConnectionManager::getInstance).thenReturn(mockConnection);
            when(mockConnection.getRemoteObject(
                "fr.enedis.HAL.ModemManager1",
                "/fr/enedis/HAL/ModemManager1",
                ModemManager1.class
            )).thenReturn(mockModemManager);

            DbusModemBG95Adapter result = DbusModemBG95Adapter.connect();

            assertThat(result).isNotNull();
        }
    }

    @Test
    void la_connexion_statique_echoue_quand_dbus_n_est_pas_disponible() throws DBusException {
        try (MockedStatic<DbusConnectionManager> mockedConnectionManager = mockStatic(DbusConnectionManager.class)) {
            DBusConnection mockConnection = mock(DBusConnection.class);
            mockedConnectionManager.when(DbusConnectionManager::getInstance).thenReturn(mockConnection);
            when(mockConnection.getRemoteObject(
                "fr.enedis.HAL.ModemManager1",
                "/fr/enedis/HAL/ModemManager1",
                ModemManager1.class
            )).thenThrow(new DBusException("Connection failed"));

            assertThatThrownBy(() -> DbusModemBG95Adapter.connect())
                .isInstanceOf(IllegalStateException.class)
                .hasMessage("Could not connect to DBus Modem Manager")
                .hasCauseInstanceOf(DBusException.class);
        }
    }

    @Test
    void les_bonnes_constantes_dbus_sont_utilisees_pour_la_connexion() throws DBusException {
        try (MockedStatic<DbusConnectionManager> mockedConnectionManager = mockStatic(DbusConnectionManager.class)) {
            DBusConnection mockConnection = mock(DBusConnection.class);
            mockedConnectionManager.when(DbusConnectionManager::getInstance).thenReturn(mockConnection);
            when(mockConnection.getRemoteObject(
                "fr.enedis.HAL.ModemManager1",
                "/fr/enedis/HAL/ModemManager1",
                ModemManager1.class
            )).thenReturn(mockModemManager);

            DbusModemBG95Adapter.connect();

            mockConnection.getRemoteObject(
                "fr.enedis.HAL.ModemManager1",
                "/fr/enedis/HAL/ModemManager1",
                ModemManager1.class
            );
        }
    }
}
