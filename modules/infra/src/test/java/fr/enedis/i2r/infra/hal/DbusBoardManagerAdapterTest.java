package fr.enedis.i2r.infra.hal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import org.freedesktop.dbus.connections.impl.DBusConnection;
import org.freedesktop.dbus.exceptions.DBusException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import fr.enedis.hal.BoardManager1;

class DbusBoardManagerAdapterTest {

    private BoardManager1 mockBoardManager;
    private DbusBoardManagerAdapter adapter;

    @BeforeEach
    void setUp() {
        mockBoardManager = mock(BoardManager1.class);
    }

    @Test
    void l_ads_est_recupere_depuis_le_board_manager() throws DBusException {
        String expectedAds = "TEST_ADS_12345";
        when(mockBoardManager.getADS()).thenReturn(expectedAds);

        try (MockedStatic<DbusConnectionManager> mockedConnectionManager = mockStatic(DbusConnectionManager.class)) {
            DBusConnection mockConnection = mock(DBusConnection.class);
            mockedConnectionManager.when(DbusConnectionManager::getInstance).thenReturn(mockConnection);
            when(mockConnection.getRemoteObject(
                "fr.enedis.HAL.BoardManager1",
                "/fr/enedis/HAL/BoardManager1",
                BoardManager1.class
            )).thenReturn(mockBoardManager);

            adapter = new DbusBoardManagerAdapter();
            String actualAds = adapter.getAds();

            assertThat(actualAds).isEqualTo(expectedAds);
        }
    }

    @Test
    void les_echecs_de_connexion_dbus_sont_geres_lors_de_la_creation() throws DBusException {
        try (MockedStatic<DbusConnectionManager> mockedConnectionManager = mockStatic(DbusConnectionManager.class)) {
            DBusConnection mockConnection = mock(DBusConnection.class);
            mockedConnectionManager.when(DbusConnectionManager::getInstance).thenReturn(mockConnection);
            when(mockConnection.getRemoteObject(
                "fr.enedis.HAL.BoardManager1",
                "/fr/enedis/HAL/BoardManager1",
                BoardManager1.class
            )).thenThrow(new DBusException("Connection failed"));

            adapter = new DbusBoardManagerAdapter();

            assertThat(adapter).isNotNull();
        }
    }

    @Test
    void une_exception_est_levee_quand_le_board_manager_n_est_pas_disponible() throws DBusException {
        try (MockedStatic<DbusConnectionManager> mockedConnectionManager = mockStatic(DbusConnectionManager.class)) {
            DBusConnection mockConnection = mock(DBusConnection.class);
            mockedConnectionManager.when(DbusConnectionManager::getInstance).thenReturn(mockConnection);
            when(mockConnection.getRemoteObject(
                "fr.enedis.HAL.BoardManager1",
                "/fr/enedis/HAL/BoardManager1",
                BoardManager1.class
            )).thenThrow(new DBusException("Connection failed"));

            adapter = new DbusBoardManagerAdapter();

            try {
                adapter.getAds();
            } catch (NullPointerException e) {
                assertThat(e).isInstanceOf(NullPointerException.class);
            }
        }
    }
}
