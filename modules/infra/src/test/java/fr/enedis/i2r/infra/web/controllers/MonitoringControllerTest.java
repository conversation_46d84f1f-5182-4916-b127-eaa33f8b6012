package fr.enedis.i2r.infra.web.controllers;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.BufferedReader;
import java.io.StringReader;
import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.system.ports.ShellExecutorPort;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;

class MonitoringControllerTest {

    @Mock
    private ComSiConfiguration configuration;
    @Mock
    private ShellExecutorPort shellExecutor;
    @Mock
    private Context context;

    private MonitoringController controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        controller = new MonitoringController(configuration, shellExecutor);
    }

    @Test
    void synchronisation_horraire_appelle_configuration_et_renvoit_reponse() {
        Duration threshold = Duration.ofSeconds(5);
        when(configuration.timeDesynchronizationThreshold()).thenReturn(threshold);
        when(context.status(any(HttpStatus.class))).thenReturn(context);

        String mockOutput = "Last offset     : 0.000123456 seconds\nLeap status     : Normal\n";
        BufferedReader mockReader = new BufferedReader(new StringReader(mockOutput));
        when(shellExecutor.execute(any())).thenReturn(mockReader);

        controller.getTimeSync(context);

        verify(configuration).timeDesynchronizationThreshold();
        verify(context).status(HttpStatus.OK);
        verify(context).result(any(String.class));
    }

    @Test
    void shell_executor_est_utilise_pour_verification_temps() {
        Duration threshold = Duration.ofSeconds(3);
        when(configuration.timeDesynchronizationThreshold()).thenReturn(threshold);
        when(context.status(any(HttpStatus.class))).thenReturn(context);

        String mockOutput = "Last offset     : -2.000004740 seconds\nLeap status     : Normal\n";
        BufferedReader mockReader = new BufferedReader(new StringReader(mockOutput));
        when(shellExecutor.execute(any())).thenReturn(mockReader);

        controller.getTimeSync(context);

        verify(configuration).timeDesynchronizationThreshold();
        verify(context).status(HttpStatus.OK);
        verify(context).result(any(String.class));
    }
}
