package fr.enedis.i2r.infra.web.controllers;

import static io.javalin.http.HttpStatus.OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import fr.enedis.i2r.comsi.errors.rest.InvalidRequestBodyException;
import fr.enedis.i2r.system.LoggingLoader;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;

class LoggingControllerTest {

    @Mock
    private LoggingLoader loggingLoader;
    @Mock
    private Context context;

    private LoggingController controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        controller = new LoggingController(loggingLoader);
    }

    @Test
    void le_niveau_de_log_change_bien_correctement() throws InvalidRequestBodyException {
        String logLevel = "ERROR";
        when(context.queryParam("logLevel")).thenReturn(logLevel);
        when(context.status(any(HttpStatus.class))).thenReturn(context);

        controller.changeLogLevel(context);

        verify(loggingLoader).changeLogLevel("ROOT", logLevel);
        verify(context).status(OK);
        verify(context).result("Le niveau de log i2R a bien été modifié à : " + logLevel);
    }

    @Test
    void une_exception_est_levee_quand_parametre_niveau_log_manquant() {
        when(context.queryParam("logLevel")).thenReturn(null);

        InvalidRequestBodyException exception = assertThrows(
            InvalidRequestBodyException.class,
            () -> controller.changeLogLevel(context)
        );

        assertEquals("contenu de la requête invalide: niveau de log manquant", exception.getMessage());
    }
}
