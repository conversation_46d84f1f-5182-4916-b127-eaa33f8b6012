package fr.enedis.i2r.infra.web.controllers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.Instant;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.ConfigurationBoitier;
import fr.enedis.i2r.comsi.errors.rest.InvalidQueryParamsException;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.ModemManagerPort;
import fr.enedis.i2r.comsi.ports.SecurityModulePort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;

class ConfigurationControllerTest {

    @Mock
    private ComSiConfiguration comSiConfiguration;
    @Mock
    private ComsiParametersPort parametersPort;
    @Mock
    private BoardManagerPort boardManagerPort;
    @Mock
    private SecurityModulePort moduleSecuritePort;
    @Mock
    private ModemManagerPort modemManagerPort;
    @Mock
    private SiClientPort siClientPort;
    @Mock
    private Context context;
    @Mock
    private ConfigurationBoitier configurationBoitier;

    private ConfigurationController controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        controller = new ConfigurationController(
            comSiConfiguration, parametersPort, boardManagerPort,
            moduleSecuritePort, modemManagerPort, siClientPort
        );
    }

    @Test
    void configuration_recuperee_avec_profondeur_par_defaut() throws Exception {
        when(context.queryParam("depth")).thenReturn(null);
        when(parametersPort.getConfigurationHash()).thenReturn("test-hash");
        when(boardManagerPort.getAds()).thenReturn("test-ads");
        when(moduleSecuritePort.getIdms()).thenReturn("test-idms");
        when(modemManagerPort.getIccid()).thenReturn("test-iccid");
        when(context.status(any(HttpStatus.class))).thenReturn(context);

        try (MockedStatic<ConfigurationBoitier> mockedConfigBoitier = Mockito.mockStatic(ConfigurationBoitier.class)) {
            mockedConfigBoitier.when(() -> ConfigurationBoitier.from(
                any(ComSiConfiguration.class),
                any(Instant.class),
                any(String.class),
                any(String.class),
                any(String.class),
                any(String.class)
            )).thenReturn(configurationBoitier);

            controller.getConfiguration(context);

            verify(parametersPort).getConfigurationHash();
            verify(boardManagerPort).getAds();
            verify(moduleSecuritePort).getIdms();
            verify(modemManagerPort).getIccid();
            verify(siClientPort).sendConfigurationBoitier(configurationBoitier);
            verify(context).status(HttpStatus.OK);
        }
    }

    @Test
    void getConfiguration_recupere_configuration_avec_profondeur_personnalisee() throws Exception {
        when(context.queryParam("depth")).thenReturn("50");
        when(parametersPort.getConfigurationHash()).thenReturn("test-hash");
        when(boardManagerPort.getAds()).thenReturn("test-ads");
        when(moduleSecuritePort.getIdms()).thenReturn("test-idms");
        when(modemManagerPort.getIccid()).thenReturn("test-iccid");
        when(context.status(any(HttpStatus.class))).thenReturn(context);

        try (MockedStatic<ConfigurationBoitier> mockedConfigBoitier = Mockito.mockStatic(ConfigurationBoitier.class)) {
            mockedConfigBoitier.when(() -> ConfigurationBoitier.from(
                any(ComSiConfiguration.class),
                any(Instant.class),
                any(String.class),
                any(String.class),
                any(String.class),
                any(String.class)
            )).thenReturn(configurationBoitier);

            controller.getConfiguration(context);

            verify(parametersPort).getConfigurationHash();
            verify(boardManagerPort).getAds();
            verify(moduleSecuritePort).getIdms();
            verify(modemManagerPort).getIccid();
            verify(siClientPort).sendConfigurationBoitier(configurationBoitier);
            verify(context).status(HttpStatus.OK);
        }
    }

    @Test
    void une_exception_est_levee_si_parametre_depth_est_invalide() {
        when(context.queryParam("depth")).thenReturn("invalid-number");

        InvalidQueryParamsException exception = assertThrows(
            InvalidQueryParamsException.class,
            () -> controller.getConfiguration(context)
        );

        assertEquals("Parametre(s) de la query invalide(s): Paramètre depth invalide: doit être un nombre entier", exception.getMessage());
    }
}
