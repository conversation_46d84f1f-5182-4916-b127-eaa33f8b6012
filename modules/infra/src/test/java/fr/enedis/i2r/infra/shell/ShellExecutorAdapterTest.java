package fr.enedis.i2r.infra.shell;

import static fr.enedis.i2r.system.shell.ShellCommand.CHRONY_TRACKING;
import static org.assertj.core.api.Assertions.assertThat;

import java.io.BufferedReader;
import java.io.IOException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ShellExecutorAdapterTest {

    private ShellExecutorAdapter adapter;

    @BeforeEach
    void setUp() {
        adapter = new ShellExecutorAdapter();
    }

    @Test
    void la_commande_chrony_tracking_retourne_du_contenu_lisible() throws IOException {
        BufferedReader reader = adapter.execute(CHRONY_TRACKING);

        assertThat(reader).isNotNull();
        reader.close();
    }
}
