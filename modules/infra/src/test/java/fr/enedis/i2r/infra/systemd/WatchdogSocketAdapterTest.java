package fr.enedis.i2r.infra.systemd;

import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;

import org.junit.jupiter.api.Test;

class WatchdogSocketAdapterTest {

    @Test
    void la_creation_reussit_avec_un_chemin_de_socket_valide() {
        String validSocketPath = "/run/systemd/notify";

        assertThatCode(() -> new WatchdogSocketAdapter(validSocketPath))
                .doesNotThrowAnyException();
    }

    @Test
    void la_creation_echoue_avec_un_chemin_de_socket_invalide() {
        String invalidSocketPath = "invalid_socket_path";

        assertThatThrownBy(() -> new WatchdogSocketAdapter(invalidSocketPath))
                .isInstanceOf(InvalidSocketTypeException.class);
    }

    @Test
    void la_creation_echoue_avec_un_chemin_null() {
        assertThatThrownBy(() -> new WatchdogSocketAdapter(null))
                .isInstanceOf(NullPointerException.class);
    }

    @Test
    void le_message_ready_est_envoye_lors_de_l_initialisation() throws InvalidSocketTypeException {
        UnixSocket mockSocket = mock(UnixSocket.class);
        doNothing().when(mockSocket).notify("READY=1");

        WatchdogSocketAdapter adapter = new WatchdogSocketAdapter("/run/systemd/notify");

        assertThatCode(() -> adapter.init())
                .doesNotThrowAnyException();
    }

    @Test
    void le_message_watchdog_est_envoye_lors_du_heartbeat() throws InvalidSocketTypeException {
        WatchdogSocketAdapter adapter = new WatchdogSocketAdapter("/run/systemd/notify");

        assertThatCode(() -> adapter.heartbeat())
                .doesNotThrowAnyException();
    }


}
