package fr.enedis.i2r.infra.rest.si.ao2.metrics;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.metrics.MetricsSet;
import fr.enedis.i2r.comsi.metrics.ServingCellMetrics;

class MetricsBoitierTest {

    @Test
    void les_metrics_json_serving_cell_sont_ajoutees() {

        ServingCellMetrics metric1 = new ServingCellMetrics(
            1300,
            ZonedDateTime.ofInstant(Instant.ofEpochSecond(1756296811L), ZoneId.systemDefault()),
            208,
            1,
            -81,
            -7,
            -58,
            -16,
            334,
            51908,
            "eMTC"
        );

        ServingCellMetrics metric2 = new ServingCellMetrics(
            1300,
            ZonedDateTime.ofInstant(Instant.ofEpochSecond(1756296828L), ZoneId.systemDefault()),
            208,
            1,
            -85,
            -3,
            -68,
            -17,
            334,
            51908,
            "eMTC"
        );

        MetricsSet metricsSet = new MetricsSet(List.of(metric1, metric2));


        // Act
        MetricsBoitier metricsBoitier = MetricsBoitier.from(metricsSet);

        // Assert
        assertThat(metricsBoitier).isNotNull();
        assertThat(metricsBoitier.getNb()).isEqualTo(2);
        assertThat(metricsBoitier.getObjects())
                .hasSize(2)
                .allMatch(obj -> obj instanceof StatCellServing);
    }

}
