package fr.enedis.i2r.infra.rest.si.ao2.metrics;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;

import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.metrics.ServingCellMetrics;

class StatCellServingTest {

    @Test
    void la_fonction_from_genere_un_objet_correct() {
        // Arrange
        ServingCellMetrics servingcell = new ServingCellMetrics(
            123,
            ZonedDateTime.ofInstant(Instant.ofEpochSecond(1756296906L), ZoneId.systemDefault()),
            208,
            10,
            -95,
            -10,
            -70,
            20,
            456,
            789,
            "LTE"
        );


        // Act
        StatCellServing stat = StatCellServing.from(servingcell);

        // Assert
        assertEquals(123, stat.getEarfcn());
        assertEquals(208, stat.getMcc());
        assertEquals(10, stat.getMnc());
        assertEquals(456, stat.getPci());
        assertEquals("LTE", stat.getTechno());
        assertEquals(-95, stat.getRsrp());
        assertEquals(-10, stat.getRsrq());
        assertEquals(-70, stat.getRssi());
        assertEquals(20, stat.getSinr());
        assertEquals(789, stat.getTac());

        assertNotNull(stat.getDateMes());
        assertThat(stat.getDateMes()).isNotEmpty();

        // Inherited Base defaults
        assertEquals("StatCellServing", stat.getClazz());
        assertEquals("StatCellServing", stat.getName());
        assertEquals("LOGDM", stat.getEntity());
        assertTrue(stat.getPub());
    }


}
