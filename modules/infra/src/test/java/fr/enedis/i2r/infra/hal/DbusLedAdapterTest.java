package fr.enedis.i2r.infra.hal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Optional;

import org.freedesktop.dbus.connections.impl.DBusConnection;
import org.freedesktop.dbus.exceptions.DBusException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import fr.enedis.hal.LEDManager1;
import fr.enedis.i2r.system.leds.LedName;
import fr.enedis.i2r.system.leds.LedStatus;

class DbusLedAdapterTest {

    private LEDManager1 mockLedManager;
    private DbusLedAdapter adapter;

    @BeforeEach
    void setUp() {
        mockLedManager = mock(LEDManager1.class);
    }

    @Test
    void les_leds_sont_configurees_correctement() throws DBusException {
        try (MockedStatic<DbusConnectionManager> mockedConnectionManager = mockStatic(DbusConnectionManager.class)) {
            DBusConnection mockConnection = mock(DBusConnection.class);
            mockedConnectionManager.when(DbusConnectionManager::getInstance).thenReturn(mockConnection);
            when(mockConnection.getRemoteObject(
                "fr.enedis.HAL.LEDManager1",
                "/fr/enedis/HAL/LEDManager1",
                LEDManager1.class
            )).thenReturn(mockLedManager);
            adapter = new DbusLedAdapter();

            adapter.setLedStatus(LedName.POWER, LedStatus.ON);
            adapter.setLedStatus(LedName.CPT, LedStatus.BLINK_GREEN);
            adapter.setLedStatus(LedName.ETH1, LedStatus.OFF);
            adapter.setLedStatus(LedName.ETH2, LedStatus.BLINK_YELLOW);
            adapter.setLedStatus(LedName.SI, LedStatus.ON);
            adapter.setLedStatus(LedName.RSL, LedStatus.BLINK_RED);

            verify(mockLedManager).setPowerLedStatus(LedStatus.ON.getValue());
            verify(mockLedManager).setCLedStatus(LedStatus.BLINK_GREEN.getValue());
            verify(mockLedManager).setEth1LedStatus(LedStatus.OFF.getValue());
            verify(mockLedManager).setEth2LedStatus(LedStatus.BLINK_YELLOW.getValue());
            verify(mockLedManager).setSILedStatus(LedStatus.ON.getValue());
            verify(mockLedManager).setRSLLedStatusAndColor(LedStatus.BLINK_RED.getValue());
        }
    }

    @Test
    void le_statut_d_une_led_est_lu_correctement() throws DBusException {
        try (MockedStatic<DbusConnectionManager> mockedConnectionManager = mockStatic(DbusConnectionManager.class);
             MockedStatic<LedStatus> mockedLedStatus = mockStatic(LedStatus.class)) {
            DBusConnection mockConnection = mock(DBusConnection.class);
            mockedConnectionManager.when(DbusConnectionManager::getInstance).thenReturn(mockConnection);
            when(mockConnection.getRemoteObject(
                "fr.enedis.HAL.LEDManager1",
                "/fr/enedis/HAL/LEDManager1",
                LEDManager1.class
            )).thenReturn(mockLedManager);
            when(mockLedManager.getPowerLedStatus()).thenReturn((byte) 4);
            mockedLedStatus.when(() -> LedStatus.fromByte((byte) 4)).thenReturn(Optional.of(LedStatus.ON));

            adapter = new DbusLedAdapter();

            LedStatus status = adapter.getLedStatus(LedName.POWER);

            assertThat(status).isEqualTo(LedStatus.ON);
        }
    }

    @Test
    void les_echecs_de_connexion_dbus_sont_geres_lors_de_la_creation() throws DBusException {
        try (MockedStatic<DbusConnectionManager> mockedConnectionManager = mockStatic(DbusConnectionManager.class)) {
            DBusConnection mockConnection = mock(DBusConnection.class);
            mockedConnectionManager.when(DbusConnectionManager::getInstance).thenReturn(mockConnection);
            when(mockConnection.getRemoteObject(
                "fr.enedis.HAL.LEDManager1",
                "/fr/enedis/HAL/LEDManager1",
                LEDManager1.class
            )).thenThrow(new DBusException("Connection failed"));

            adapter = new DbusLedAdapter();

            assertThat(adapter).isNotNull();
        }
    }
}
