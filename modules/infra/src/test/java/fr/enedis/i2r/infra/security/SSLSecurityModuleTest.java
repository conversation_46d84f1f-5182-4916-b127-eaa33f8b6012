package fr.enedis.i2r.infra.security;

import static org.assertj.core.api.Assertions.assertThat;

import javax.net.ssl.SSLContext;

import org.junit.jupiter.api.Test;

import fr.enedis.i2r.infra.security.errors.SSLSecurityModuleException;
import io.javalin.community.ssl.SslPlugin;

class SSLSecurityModuleTest {

    @Test
    void le_contexte_ssl_retourne_est_valide() throws Exception {
        SSLSecurityModule module = new SSLSecurityModule();

        SSLContext sslContext = module.getSslContext();

        assertThat(sslContext).isNotNull();
    }

    @Test
    void le_plugin_ssl_retourne_est_valide() throws Exception {
        SSLSecurityModule module = new SSLSecurityModule();

        SslPlugin sslPlugin = module.getSslPlugin();

        assertThat(sslPlugin).isNotNull();
        assertThat(sslPlugin).isInstanceOf(SslPlugin.class);
    }

    @Test
    void les_erreurs_de_creation_sont_gerees() {
        try {
            new SSLSecurityModule();
        } catch (SSLSecurityModuleException e) {
            assertThat(e.getMessage()).contains("Impossible de créer le SSLContext");
        }
    }

}
