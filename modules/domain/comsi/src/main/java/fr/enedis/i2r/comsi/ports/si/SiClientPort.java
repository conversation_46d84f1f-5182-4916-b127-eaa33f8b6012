package fr.enedis.i2r.comsi.ports.si;

import fr.enedis.i2r.comsi.ConfigurationBoitier;
import fr.enedis.i2r.comsi.errors.RequestToSiException;
import fr.enedis.i2r.comsi.metrics.MetricsSet;

public interface SiClientPort {
    void sendConfigurationBoitier(ConfigurationBoitier config) throws RequestToSiException;
    void sendMetricsBoitier(MetricsSet metrics) throws RequestToSiException;

    SiConfigurationNotifierPort getSiConfigurationNotifier();
}
