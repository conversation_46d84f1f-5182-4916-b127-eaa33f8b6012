package fr.enedis.i2r.comsi.metrics;

import java.util.List;

import fr.enedis.i2r.comsi.errors.MetricsGatheringException;

public interface MetricsSourcePort {
    // ALLAN: Avoir fait une exception pour la récupération de tes métriques est
    // une bonne chose. En Java, la gestion d'erreurs se fait via des exceptions.
    // Savoir si une méthode s'est bien déroulée ou non ne doit pas se traduire
    // par un booléen sur une méthode void ou un null, et tu l'as bien compris.
    //
    // La seule correction ici c'est sur la nomenclature de ton exception. Ton
    // exception représente l'erreur qu'il s'est passée.
    // Tu as appellé ton exception "MetricsException", et ça veut juste dire "ERREUR METRIQUE"
    // mais ça veut dire quoi ?
    // ici il faut mettre en avant qu'il s'agit d'une erreur de récupération de métrique.
    List<MetricsSet> getAvailableMetrics() throws MetricsGatheringException;

    void removeMetrics(MetricsSet metricsSet);
}
