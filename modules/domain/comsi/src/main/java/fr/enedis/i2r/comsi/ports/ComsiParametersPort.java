package fr.enedis.i2r.comsi.ports;

import java.time.LocalTime;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.errors.InvalidParameterException;
import fr.enedis.i2r.comsi.errors.RequiredParameterMissingException;
import fr.enedis.i2r.comsi.status.BipStatus;

public interface ComsiParametersPort {
    String getConfigurationHash();

    ComSiConfiguration getComSiConfiguration() throws RequiredParameterMissingException, InvalidParameterException;

    void updateMetricsSendTime(LocalTime metricsSendTime);

    void updateBipStatus(BipStatus status);
}
