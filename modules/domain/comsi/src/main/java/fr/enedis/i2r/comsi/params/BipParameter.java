package fr.enedis.i2r.comsi.params;

import static java.util.stream.Collectors.toMap;

import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;

public enum BipParameter {
    // pub == hash == watched.
    // Tout paramètre à publier est à hasher et surveiller pour le changement, et vice versa.
    BipState("i2r.bip.status", true),
    ConnectionCheckInterval("i2r.network.connection-check-interval-secs", true),
    PingPeriodInMs("i2r.si.ping.period-ms", true),
    PingTimeoutInMs("i2r.si.ping.timeout-ms", true),
    ModemConnectionWaitAfterReset("i2r.network.modem.connection.wait.seconds-delay-after-modem-reset", true),
    MaximumRandomDelayBetweenPings("i2r.si.ping.maximum-random-delay-ms", true),
    FailedPingsLimitBeforeWanReset("i2r.si.ping.consecutive-failed-pings-limit-before-wan-reset", true),
    ModemResetsLimitBeforeBipReboot("i2r.si.ping.consecutive-modem-resets-limit-before-bip-reboot", true),
    DomainOrange("i2r.network.domain.orange", true),
    ApnOrange("i2r.network.apn.orange", true),
    ApnBouygues("i2r.network.apn.bouygues", true),
    ApnSfr("i2r.network.apn.sfr", true),
    PacyIpv4("i2r.si.ip.pacy", true),
    NoeIpv4("i2r.si.ip.noe", true),
    TimeDesynchronizationThreshold("i2r.si.time.sync.threshold", true),
    SignalCheckInterval("i2r.network.signal-check-interval-secs", true),
    WatchdogIntervalI2rApp("i2r.watchdog-interval-secs.i2r-app", true),
    WatchdogIntervalI2rNetwork("i2r.watchdog-interval-secs.i2r-network", true);

    public final String parameterKey;
    public final boolean watched;

    private static final Map<String, BipParameter> BY_KEY = Stream.of(values())
            .collect(toMap(param -> param.parameterKey, Function.identity()));

    BipParameter(String parameterKey, boolean watched) {
        this.parameterKey = parameterKey;
        this.watched = watched;
    }

    public static Optional<BipParameter> fromParameterKey(String parameterKey) {
        return Optional.ofNullable(BY_KEY.get(parameterKey));
    }
}
