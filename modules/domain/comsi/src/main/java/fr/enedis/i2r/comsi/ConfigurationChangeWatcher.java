package fr.enedis.i2r.comsi;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.params.ConfigurationValue;
import fr.enedis.i2r.comsi.ports.ConfigurationUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.si.SiConfigurationNotifierPort;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;
public class ConfigurationChangeWatcher implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationChangeWatcher.class);

    private ConfigurationUpdateWatcherPort configurationUpdateWatcher;
    private SiConfigurationNotifierPort siNotifierPort;
    private ThreadWatchdog threadWatchdog;
    private volatile boolean running = true;

    public ConfigurationChangeWatcher(ConfigurationUpdateWatcherPort configurationUpdateWatcher, SiConfigurationNotifierPort siNotifierPort, ThreadWatchdog threadWatchdog) {
        this.configurationUpdateWatcher = configurationUpdateWatcher;
        this.siNotifierPort = siNotifierPort;
        this.threadWatchdog = threadWatchdog;
    }

    @Override
    public void run() {
        threadWatchdog.register(ConfigurationChangeWatcher.class.getSimpleName());
        logger.info("Configuration change watcher started");

        while (running && !Thread.currentThread().isInterrupted()) {
            try {
                List<ConfigurationValue> updatedValues = this.configurationUpdateWatcher
                    .waitForUpdates()
                    .stream().filter(confValue -> confValue.parameter().watched)
                    .toList();

                for (ConfigurationValue updatedValue: updatedValues) {
                    this.notifySi(updatedValue);
                }
            } catch (InterruptedException e) {
                logger.info("Configuration change watcher interrupted, stopping...");
                Thread.currentThread().interrupt();
                break;
            } catch (Throwable e) {
                logger.error("erreur lors de l'écoute d'un changement de configuration", e);
            }
        }
    }

    public void stop() {
        logger.info("Stopping configuration change watcher...");
        running = false;
    }

    private void notifySi(ConfigurationValue updatedConfiguration) throws Exception {
        this.handleStateChange(updatedConfiguration.value());
    }

    private void handleStateChange(String newState) throws NumberFormatException, Exception {
        Integer stateValue = Integer.parseInt(newState);

        BipStatus bipStatus = BipStatus.fromStatusCode(stateValue)
            .orElseThrow(() -> new Exception(String.format("nouvel état du boitier invalide: %d", stateValue)));

        this.siNotifierPort.notifyStateChange(bipStatus);
    }
}
