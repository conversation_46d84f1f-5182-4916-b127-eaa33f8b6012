package fr.enedis.i2r.comsi;

import java.time.Duration;
import java.time.LocalTime;
import java.util.Optional;

import fr.enedis.i2r.comsi.status.BipStatus;

public record ComSiConfiguration(
        Duration periodBetweenPings,
        Duration maximumRandomPeriodBetweenPings,
        Duration pingTimeout,
        IpAddress noeIpv4,
        IpAddress pacyIpv4,
        Integer failedPingsLimitBeforeModemReset,
        Integer modemResetsLimitBeforeAo3Reboot,
        Integer netmaskSize,
        Integer pingRetryLimit,
        Duration timeDesynchronizationThreshold,
        Datacenter primaryDatacenter,
        BipStatus bipStatus,
        LocalTime metricsSendIntervalStart,
        Duration metricsSendIntervalMaximumLength,
        Optional<LocalTime> metricsSendTime) {

    public static final Duration DEFAULT_PERIOD_BETWEEN_PINGS = Duration.ofMinutes(5);
    public static final Duration DEFAULT_MAXIMUM_RANDOM_PERIOD_BETWEEN_PINGS = Duration.ofMinutes(1);
    public static final Duration DEFAULT_PING_TIMEOUT = Duration.ofSeconds(5);
    public static final IpAddress DEFAULT_NOE_IPV4 = new IpAddress("***********");
    public static final IpAddress DEFAULT_PACY_IPV4 = new IpAddress("***********");
    public static final Integer DEFAULT_FAILED_PINGS_LIMIT_BEFORE_MODEM_RESET = 3;
    public static final Integer DEFAULT_MODEM_RESETS_LIMIT_BEFORE_AO3_REBOOT = 2;
    public static final Integer DEFAULT_NETMASK_SIZE = 24;
    public static final Integer DEFAULT_PING_RETRY_LIMIT = 3;
    public static final Duration DEFAULT_TIME_DESYNCHRONIZATION_THRESHOLD = Duration.ofSeconds(1);
    public static final BipStatus DEFAULT_BIP_STATUS = BipStatus.INIT;
    public static final LocalTime DEFAULT_METRICS_SEND_INTERVAL_START = LocalTime.parse("01:00:00");
    public static final Duration DEFAULT_METRICS_SEND_INTERVAL_MAXIMUM_LENGTH = Duration.ofMinutes(120);
    public static final Optional<LocalTime> DEFAULT_METRICS_SEND_TIME = Optional.empty();

    public DatacenterConfiguration parseDatacenterConfiguration() {
        return DatacenterConfiguration.from(this);
    }
}
