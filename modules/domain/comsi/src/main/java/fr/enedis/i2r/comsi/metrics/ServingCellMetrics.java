package fr.enedis.i2r.comsi.metrics;

import java.time.ZonedDateTime;

public record ServingCellMetrics(
    /** Numéro de canal assigné */
    Integer earfcn,
    /** Horodate de la mesure du signal */
    ZonedDateTime emittedTime,
    /** Code pays du PLMN dans le plan GSM */
    Integer mcc,
    /** Code réseau du PLMN dans le plan GSM */
    Integer mnc,
    /** Puissance reçue du signal de référence en valeur absolue en dbm */
    Integer rsrp,
    /** Qualité de réception du signal de référence */
    Integer rsrq,
    /** Mesure de la puissance brute du signal radio reçu en large bande */
    Integer rssi,
    /** Rapport entre le signal utile et les interférences plus le bruit. Un SINR élevé indique un signal clair avec peu d'interférences. */
    Integer sinr,
    /** Identifiant unique d'une cellule  */
    Integer pci,
    /** Code d’identification de la zone géographique au sein du PLMN */
    Integer tac,
    /** Technologie utilisée: LTE, LTE-M, NB-IOT ... */
    String techno
) implements Metric {}
