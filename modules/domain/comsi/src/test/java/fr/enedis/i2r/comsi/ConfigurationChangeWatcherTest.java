package fr.enedis.i2r.comsi;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.params.BipParameter;
import fr.enedis.i2r.comsi.params.ConfigurationValue;
import fr.enedis.i2r.comsi.ports.ConfigurationUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.si.SiConfigurationNotifierPort;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;

class ConfigurationChangeWatcherTest {

    private ConfigurationUpdateWatcherPort mockDatabaseUpdateWatcherPort;
    private SiConfigurationNotifierPort mockSiNotifierPort;
    private ConfigurationChangeWatcher watcher;
    private ExecutorService executor;

    @BeforeEach
    void setUp() {
        mockDatabaseUpdateWatcherPort = mock(ConfigurationUpdateWatcherPort.class);
        mockSiNotifierPort = mock(SiConfigurationNotifierPort.class);
        watcher = new ConfigurationChangeWatcher(mockDatabaseUpdateWatcherPort, mockSiNotifierPort, mock(ThreadWatchdog.class));
        executor = Executors.newSingleThreadExecutor();
    }

    @AfterEach
    void tearDown() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdownNow();
        }
    }

    @Test
    void arret_gracieux_avec_methode_stop() throws InterruptedException {
        CountDownLatch startedLatch = new CountDownLatch(1);
        CountDownLatch stoppedLatch = new CountDownLatch(1);
        when(mockDatabaseUpdateWatcherPort.waitForUpdates()).thenAnswer(invocation -> {
            startedLatch.countDown();
            try {
                Thread.sleep(100);
                return List.of();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw e;
            }
        });

        executor.submit(() -> {
            try {
                watcher.run();
            } finally {
                stoppedLatch.countDown();
            }
        });

        assertThat(startedLatch.await(1, TimeUnit.SECONDS)).isTrue();

        watcher.stop();

        assertThat(stoppedLatch.await(2, TimeUnit.SECONDS)).isTrue();
    }

    @Test
    void arret_avec_interruption_thread() throws InterruptedException {
        CountDownLatch startedLatch = new CountDownLatch(1);
        CountDownLatch stoppedLatch = new CountDownLatch(1);

        when(mockDatabaseUpdateWatcherPort.waitForUpdates()).thenAnswer(invocation -> {
            startedLatch.countDown();
            try {
                Thread.sleep(5000);
                return List.of();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw e;
            }
        });

        executor.submit(() -> {
            try {
                watcher.run();
            } finally {
                stoppedLatch.countDown();
            }
        });

        assertThat(startedLatch.await(1, TimeUnit.SECONDS)).isTrue();

        executor.shutdownNow();

        assertThat(stoppedLatch.await(2, TimeUnit.SECONDS)).isTrue();
    }

    @Test
    void prend_en_compte_mises_a_jour_configuration() throws InterruptedException {
        CountDownLatch processedLatch = new CountDownLatch(1);
        ConfigurationValue testValue = new ConfigurationValue(BipParameter.BipState, "1");

        when(mockDatabaseUpdateWatcherPort.waitForUpdates())
            .thenReturn(List.of(testValue))
            .thenAnswer(invocation -> {
                processedLatch.countDown();
                watcher.stop();
                return List.of();
            });

        executor.submit(watcher);

        assertThat(processedLatch.await(2, TimeUnit.SECONDS)).isTrue();
    }
}
