package fr.enedis.i2r.comsi.metrics;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.Duration;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.CustomComSiConfiguration;
import fr.enedis.i2r.comsi.errors.MetricsGatheringException;
import fr.enedis.i2r.comsi.errors.RequestToSiException;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;

class MetricsSenderTest {

    private ComsiParametersPort parametersPort;
    private SiClientPort siClientPort;
    private MetricsSourcePort metricsSource;
    private CustomComSiConfiguration configuration;

    @BeforeEach
    void setUp() {
        parametersPort = mock(ComsiParametersPort.class);
        siClientPort = mock(SiClientPort.class);
        metricsSource = mock(MetricsSourcePort.class);
        configuration = new CustomComSiConfiguration();
    }

    @Test
    void si_metrics_sending_time_est_definie_alors_sa_valeur_n_est_pas_change() {
        var conf = new CustomComSiConfiguration();
        conf.metricsSendingTime = Optional.of(LocalTime.now());
        var metricsSender = new MetricsSender(conf.build(), parametersPort, siClientPort, metricsSource);

        metricsSender.run();
        verify(parametersPort, never()).updateMetricsSendTime(any());
    }

    @Test
    void metrics_sending_time_est_initialise_si_aucune_valeur_n_est_definie() {
        var conf = new CustomComSiConfiguration();
        var metricsSender = new MetricsSender(conf.build(), parametersPort, siClientPort, metricsSource);

        metricsSender.run();

        verify(parametersPort).updateMetricsSendTime(any());
    }

    @Test
    void envoi_metriques_recupere_les_metriques_disponibles() throws MetricsGatheringException {
        var metricsSet = new MetricsSet(Collections.emptyList());
        when(metricsSource.getAvailableMetrics()).thenReturn(List.of(metricsSet));
        var metricsSender = new MetricsSender(configuration.build(), parametersPort, siClientPort, metricsSource);

        metricsSender.sendMetrics();

        verify(metricsSource).getAvailableMetrics();
    }

    @Test
    void envoi_metriques_envoie_chaque_ensemble_de_metriques_au_si() throws MetricsGatheringException, RequestToSiException {
        var metricsSet1 = new MetricsSet(Collections.emptyList());
        var metricsSet2 = new MetricsSet(Collections.emptyList());
        when(metricsSource.getAvailableMetrics()).thenReturn(List.of(metricsSet1, metricsSet2));
        var metricsSender = new MetricsSender(configuration.build(), parametersPort, siClientPort, metricsSource);

        metricsSender.sendMetrics();

        verify(siClientPort, times(2)).sendMetricsBoitier(any(MetricsSet.class));
    }

    @Test
    void envoi_metriques_supprime_les_metriques_apres_envoi_reussi() throws MetricsGatheringException {
        var metricsSet = new MetricsSet(Collections.emptyList());
        when(metricsSource.getAvailableMetrics()).thenReturn(List.of(metricsSet));
        var metricsSender = new MetricsSender(configuration.build(), parametersPort, siClientPort, metricsSource);

        metricsSender.sendMetrics();

        verify(metricsSource).removeMetrics(metricsSet);
    }

    @Test
    void envoi_metriques_ne_supprime_pas_les_metriques_si_echec_envoi_au_si() throws MetricsGatheringException, RequestToSiException {
        var metricsSet = new MetricsSet(Collections.emptyList());
        when(metricsSource.getAvailableMetrics()).thenReturn(List.of(metricsSet));
        doThrow(new RequestToSiException("Erreur SI")).when(siClientPort).sendMetricsBoitier(metricsSet);
        var metricsSender = new MetricsSender(configuration.build(), parametersPort, siClientPort, metricsSource);

        metricsSender.sendMetrics();

        verify(metricsSource, never()).removeMetrics(metricsSet);
    }

    @Test
    void envoi_metriques_continue_meme_si_erreur_sur_un_ensemble() throws MetricsGatheringException, RequestToSiException {
        var metricsSet1 = new MetricsSet(List.of(mock(Metric.class)));
        var metricsSet2 = new MetricsSet(List.of(mock(Metric.class)));
        when(metricsSource.getAvailableMetrics()).thenReturn(List.of(metricsSet1, metricsSet2));
        doThrow(new RequestToSiException("Erreur SI")).when(siClientPort).sendMetricsBoitier(metricsSet1);
        var metricsSender = new MetricsSender(configuration.build(), parametersPort, siClientPort, metricsSource);

        metricsSender.sendMetrics();

        verify(siClientPort, times(2)).sendMetricsBoitier(any(MetricsSet.class));
        verify(metricsSource).removeMetrics(metricsSet2);
        verify(metricsSource, never()).removeMetrics(metricsSet1);
    }

    @Test
    void envoi_metriques_gere_les_erreurs_de_recuperation_des_metriques() throws MetricsGatheringException, RequestToSiException {
        when(metricsSource.getAvailableMetrics()).thenThrow(new MetricsGatheringException("Erreur", new RuntimeException()));
        var metricsSender = new MetricsSender(configuration.build(), parametersPort, siClientPort, metricsSource);

        metricsSender.sendMetrics();

        verify(siClientPort, never()).sendMetricsBoitier(any());
    }

    @Test
    void initialisation_heure_envoi_genere_heure_aleatoire_dans_intervalle() {
        configuration.metricsSendIntervalStart = LocalTime.of(2, 0);
        configuration.defaultMetricsSendIntervalMaximumLength = Duration.ofHours(2);
        var metricsSender = new MetricsSender(configuration.build(), parametersPort, siClientPort, metricsSource);

        metricsSender.run();

        verify(parametersPort).updateMetricsSendTime(any(LocalTime.class));
    }
}
