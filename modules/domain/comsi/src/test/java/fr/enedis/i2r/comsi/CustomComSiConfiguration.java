package fr.enedis.i2r.comsi;

import java.time.Duration;
import java.time.LocalTime;
import java.util.Optional;

import fr.enedis.i2r.comsi.status.BipStatus;

public class CustomComSiConfiguration {
    public Duration delaiEntreChaquePing = ComSiConfiguration.DEFAULT_PERIOD_BETWEEN_PINGS;
    public Duration delaiAleatoireMaximumEntreChaquePing = ComSiConfiguration.DEFAULT_MAXIMUM_RANDOM_PERIOD_BETWEEN_PINGS;
    public Duration dureeMaximumDePing = ComSiConfiguration.DEFAULT_PING_TIMEOUT;
    public IpAddress ipNoe = ComSiConfiguration.DEFAULT_NOE_IPV4;
    public IpAddress ipPacy = ComSiConfiguration.DEFAULT_PACY_IPV4;
    public Integer limiteTentativesDePingAvantResetModem = ComSiConfiguration.DEFAULT_FAILED_PINGS_LIMIT_BEFORE_MODEM_RESET;
    public Integer limiteDeResetsModemAvantRebootBoitier = ComSiConfiguration.DEFAULT_MODEM_RESETS_LIMIT_BEFORE_AO3_REBOOT;
    public Integer tailleDeMasqueReseau = ComSiConfiguration.DEFAULT_NETMASK_SIZE;
    public Integer tentativesLorsDUnPing = ComSiConfiguration.DEFAULT_PING_RETRY_LIMIT;
    public Duration seuilDesynchronisationHoraire = ComSiConfiguration.DEFAULT_TIME_DESYNCHRONIZATION_THRESHOLD;
    public Datacenter primaryDatacenter = Datacenter.PACY;
    public BipStatus bipStatus = BipStatus.INIT;
    public Optional<LocalTime> metricsSendingTime = Optional.empty();
    public LocalTime metricsSendIntervalStart = ComSiConfiguration.DEFAULT_METRICS_SEND_INTERVAL_START;
    public Duration defaultMetricsSendIntervalMaximumLength = ComSiConfiguration.DEFAULT_METRICS_SEND_INTERVAL_MAXIMUM_LENGTH;

    public ComSiConfiguration build() {
        return new ComSiConfiguration(delaiEntreChaquePing, delaiAleatoireMaximumEntreChaquePing,
            dureeMaximumDePing, ipNoe, ipPacy, limiteTentativesDePingAvantResetModem,
            limiteDeResetsModemAvantRebootBoitier, tailleDeMasqueReseau, tentativesLorsDUnPing,
            seuilDesynchronisationHoraire, primaryDatacenter, bipStatus, metricsSendIntervalStart,
            defaultMetricsSendIntervalMaximumLength, metricsSendingTime);
    }
}
