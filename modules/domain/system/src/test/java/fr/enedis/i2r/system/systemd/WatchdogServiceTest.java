package fr.enedis.i2r.system.systemd;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import fr.enedis.i2r.system.SystemConfiguration;
import fr.enedis.i2r.system.ports.WatchdogSocketPort;
import fr.enedis.i2r.system.watchdog.DeadlockDetector;

class WatchdogServiceTest {

    @Mock
    private WatchdogSocketPort mockNotifySocket;

    @Mock
    private SystemConfiguration mockConfiguration;

    @Mock
    private DeadlockDetector mockDeadlockDetector;

    private WatchdogService watchdogService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(mockConfiguration.watchdogPeriod()).thenReturn(Duration.ofMillis(10));
    }

    @Test
    void le_service_initialise_la_socket_et_envoie_des_heartbeats() throws Exception {
        watchdogService = new WatchdogService(mockNotifySocket, mockConfiguration, mockDeadlockDetector);

        Thread serviceThread = new Thread(() -> {
            try {
                Thread.sleep(25);
                watchdogService.stop();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });

        serviceThread.start();
        watchdogService.run();
        serviceThread.join();

        verify(mockNotifySocket).init();
        verify(mockNotifySocket, atLeast(1)).heartbeat();
        verify(mockDeadlockDetector, atLeast(1)).checkAndHandleDeadlocks();
    }

    @Test
    void le_service_gere_les_interruptions_correctement() throws Exception {
        watchdogService = new WatchdogService(mockNotifySocket, mockConfiguration, mockDeadlockDetector);

        Thread serviceThread = new Thread(() -> {
            watchdogService.run();
        });

        serviceThread.start();
        Thread.sleep(20);
        serviceThread.interrupt();
        serviceThread.join(1000);

        assertThat(serviceThread.isInterrupted()).isTrue();
        verify(mockNotifySocket).init();
    }

    @Test
    void le_service_gere_les_exceptions_pendant_l_initialisation() throws Exception {
        doThrow(new RuntimeException("Init error")).when(mockNotifySocket).init();
        watchdogService = new WatchdogService(mockNotifySocket, mockConfiguration, mockDeadlockDetector);

        assertThatCode(() -> watchdogService.run()).doesNotThrowAnyException();

        verify(mockNotifySocket).init();
    }

    @Test
    void la_methode_stop_arrete_la_boucle_du_service() {
        watchdogService = new WatchdogService(mockNotifySocket, mockConfiguration, mockDeadlockDetector);

        assertThatCode(() -> watchdogService.stop()).doesNotThrowAnyException();
    }
}
