package fr.enedis.i2r.system;

import static org.assertj.core.api.Assertions.assertThatCode;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import ch.qos.logback.classic.Level;
import fr.enedis.i2r.system.ports.SystemParametersPort;

class LoggingLoaderTest {

    @Mock
    private SystemParametersPort mockParameterPort;

    private LoggingLoader loggingLoader;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        loggingLoader = new LoggingLoader(mockParameterPort);
    }

    @Test
    void le_changement_de_niveau_avec_un_niveau_valide_fonctionne() {
        assertThatCode(() -> loggingLoader.changeLogLevel("fr.enedis.i2r", "DEBUG"))
            .doesNotThrowAnyException();

        verify(mockParameterPort).setLogLevel(Level.DEBUG);
    }

    @Test
    void le_changement_de_niveau_avec_un_niveau_invalide_utilise_info_par_defaut() {
        assertThatCode(() -> loggingLoader.changeLogLevel("fr.enedis.i2r", "INVALID_LEVEL"))
            .doesNotThrowAnyException();

        verify(mockParameterPort).setLogLevel(Level.INFO);
    }

    @Test
    void le_changement_de_niveau_gere_les_exceptions_du_port() {
        doThrow(new RuntimeException("Parameter error")).when(mockParameterPort).setLogLevel(any());

        assertThatCode(() -> loggingLoader.changeLogLevel("fr.enedis.i2r", "ERROR"))
            .doesNotThrowAnyException();

        verify(mockParameterPort).setLogLevel(Level.ERROR);
    }
}
