package fr.enedis.i2r.system;

import static org.assertj.core.api.Assertions.assertThatCode;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import fr.enedis.i2r.system.leds.LedName;
import fr.enedis.i2r.system.leds.LedStatus;
import fr.enedis.i2r.system.ports.LedPort;
import fr.enedis.i2r.system.ports.SystemdPort;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;

class SystemModuleTest {

    @Mock
    private SystemConfiguration mockConfiguration;

    @Mock
    private LedPort mockLedPort;

    @Mock
    private SystemdPort mockSystemdPort;

    @Mock
    private ThreadWatchdog mockThreadWatchdog;

    private SystemModule systemModule;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        systemModule = new SystemModule(mockConfiguration, mockLedPort, mockSystemdPort, mockThreadWatchdog);
    }

    @Test
    void l_execution_enregistre_le_thread_et_allume_la_led_power() {
        when(mockLedPort.getLedStatus(LedName.POWER)).thenReturn(LedStatus.ON);

        systemModule.run();

        verify(mockThreadWatchdog).register("SystemModule");
        verify(mockLedPort).setLedStatus(LedName.POWER, LedStatus.ON);
        verify(mockLedPort).getLedStatus(LedName.POWER);
    }

    @Test
    void l_execution_gere_les_exceptions_pendant_l_allumage_des_leds() {
        doThrow(new RuntimeException("LED error")).when(mockLedPort).setLedStatus(any(), any());

        assertThatCode(() -> systemModule.run()).doesNotThrowAnyException();

        verify(mockThreadWatchdog).register("SystemModule");
        verify(mockLedPort).setLedStatus(LedName.POWER, LedStatus.ON);
    }

    @Test
    void la_configuration_de_la_led_rsl_fonctionne() {
        systemModule.setRslLed(LedStatus.BLINK);

        verify(mockLedPort).setLedStatus(LedName.RSL, LedStatus.BLINK);
    }

    @Test
    void l_activation_des_services_secondaires_utilise_la_configuration() {
        List<String> services = Arrays.asList("service1.service", "service2.service");
        when(mockConfiguration.secondaryServices()).thenReturn(services);

        systemModule.activateSecondaryServices();

        verify(mockSystemdPort).enableService(services);
    }

    @Test
    void le_demarrage_des_services_secondaires_utilise_la_configuration() {
        List<String> services = Arrays.asList("service1.service", "service2.service");
        when(mockConfiguration.secondaryServices()).thenReturn(services);

        systemModule.startSecondaryServices();

        verify(mockSystemdPort).startService(services);
    }
}
