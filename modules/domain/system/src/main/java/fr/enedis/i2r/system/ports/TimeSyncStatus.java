package fr.enedis.i2r.system.ports;

import java.time.Duration;
import java.util.Optional;

public record TimeSyncStatus(
    boolean isSynchronized,
    Optional<Duration> lastOffset,
    Optional<String> statusMessage,
    Optional<String> additionalInfo
) {

    public static TimeSyncStatus synchronizedStatus(Duration lastOffset) {
        return new TimeSyncStatus(
            true,
            Optional.of(lastOffset),
            Optional.of("Synchronized"),
            Optional.empty()
        );
    }

    public static TimeSyncStatus notSynchronized(String reason) {
        return new TimeSyncStatus(
            false,
            Optional.empty(),
            Optional.of(reason),
            Optional.empty()
        );
    }

    public static TimeSyncStatus withDetails(
            boolean isSynchronized,
            Duration lastOffset,
            String statusMessage,
            String additionalInfo) {
        return new TimeSyncStatus(
            isSynchronized,
            Optional.ofNullable(lastOffset),
            Optional.ofNullable(statusMessage),
            Optional.ofNullable(additionalInfo)
        );
    }
}
