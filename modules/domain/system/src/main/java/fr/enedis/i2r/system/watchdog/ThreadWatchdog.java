package fr.enedis.i2r.system.watchdog;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.system.ports.SleeperPort;
import fr.enedis.i2r.system.systemd.WatchdogService;

public class ThreadWatchdog {

    private final Logger logger = LoggerFactory.getLogger(ThreadWatchdog.class);

    private final ConcurrentMap<String, ThreadInfo> monitoredThreads = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler;
    private Optional<ScheduledFuture<?>> timeoutChecker = Optional.empty();
    private Optional<WatchdogService> watchdogService = Optional.empty();
    private Duration heartbeatInterval = Duration.ofSeconds(45);
    private Duration threadTimeout = heartbeatInterval.multipliedBy(2);

    private SleeperPort sleeper;

    public ThreadWatchdog(SleeperPort sleeper) {
        this.sleeper = sleeper;
        this.scheduler = Executors.newScheduledThreadPool(10);
    }

    public void setHeartbeatInterval(Duration interval) {
        this.heartbeatInterval = interval;
        this.threadTimeout = heartbeatInterval.multipliedBy(2);
    }

    public void setWatchdogService(WatchdogService watchdogService) {
        this.watchdogService = Optional.ofNullable(watchdogService);
    }

    public void register(String threadName) {
        Thread currentThread = Thread.currentThread();
        monitoredThreads.put(threadName, new ThreadInfo(currentThread, Instant.now()));
        startHeartbeat(threadName);
        startTimeoutChecker();
        logger.info("Thread registered: {}", threadName);
    }

    private void startTimeoutChecker() {
        if (timeoutChecker.isEmpty() || timeoutChecker.get().isCancelled()) {
            ScheduledFuture<?> checker = scheduler.scheduleAtFixedRate(
                this::checkForDeadThreads,
                threadTimeout.toMillis(),
                heartbeatInterval.toMillis(),
                TimeUnit.MILLISECONDS
            );
            timeoutChecker = Optional.of(checker);
            logger.info("Threads Watchdog started (timeout: {}s)", threadTimeout.toSeconds());
        }
    }

    private void checkForDeadThreads() {
        for (Map.Entry<String, ThreadInfo> entry : monitoredThreads.entrySet()) {
            String threadName = entry.getKey();
            ThreadInfo threadInfo = entry.getValue();
            if (isThreadDead(threadInfo)) {
                logger.error("DEAD THREAD: {} - triggering restart", threadName);
                // Stop the WatchdogService to prevent further heartbeats to systemd
                watchdogService.ifPresent(
                    service -> {
                        service.stop();
                        logger.info("WatchdogService stopped - systemd will restart the service");
                    }
                );
                try {
                    sleeper.sleep(heartbeatInterval);
                    logger.error("WatchdogService is still sending heartbeats - Forcing systemd restart");
                    System.exit(1);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    private void startHeartbeat(String threadName) {
        ScheduledFuture<?> future = scheduler.scheduleAtFixedRate(
            () -> heartbeat(threadName),
            heartbeatInterval.toMillis(),
            heartbeatInterval.toMillis(),
            TimeUnit.MILLISECONDS
        );
        ThreadInfo threadInfo = monitoredThreads.get(threadName);
        if (threadInfo != null) {
            threadInfo.setScheduledHeartbeat(future);
        }
        logger.debug("Heartbeat started: {}", threadName);
    }

    private void heartbeat(String threadName) {
        ThreadInfo threadInfo = monitoredThreads.get(threadName);
        if (!isThreadDead(threadInfo)) {
            logger.debug("Heartbeat timestamp recorded for thread: {}", threadName);
            threadInfo.setLastHeartbeat(Instant.now());
        }
    }

    public void shutdown() {
        // Cancel all scheduled heartbeats
        monitoredThreads.values().forEach(threadInfo -> {
            ScheduledFuture<?> future = threadInfo.getScheduledHeartbeat();
            if (future != null && !future.isCancelled()) {
                future.cancel(false);
            }
        });
        monitoredThreads.clear();

        // Stop timeout checker if running
        timeoutChecker.ifPresent(future -> {
            if (!future.isCancelled()) {
                future.cancel(false);
            }
        });
        timeoutChecker = Optional.empty();

        scheduler.shutdown();
        logger.info("ThreadWatchdog shutdown");
    }

    private boolean isThreadDead(ThreadInfo threadInfo) {
        Thread thread = threadInfo.getThread();
        return threadInfo.getLastHeartbeat().isBefore(Instant.now().minus(threadTimeout))
            || thread == null
            || !thread.isAlive()
            || thread.isInterrupted()
            || thread.getState() == Thread.State.TERMINATED;
    }
}
