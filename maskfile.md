# Commandes du projet i2R

Ce fichier recense toutes les commandes utiles au développement et au build du
projet i2R. Vous pouvez :

- Copier-coller les commandes depuis ce fichier
- Utiliser l'outil en ligne de commande [mask](https://github.com/jacobdeichert/mask), qui se base sur ce fichier afin d'exécuter les commandes
  - Par exemple : `mask run boitier-ui`

## clean

> Cette commande permet de nettoyer les artefacts Java de ce dépot.

```sh
./mvnw clean
```

## run

> Concerne le lancement des applications i2R.

### run boitier-ui

> Démarre l'interface du boitier UI

```sh
cd apps/boitier-ui
deno task start
```

### run main

> Démarre l'application i2R, pour qu'elle fonctionne correctement en local il est nécessaire de démarrer ses dépendances 
> également : embedded-hal et si-mock

```sh
echo "Compilation du main en cours..."
mask install
echo "Compilation terminée"
echo "Lancement de i2R..."
./mvnw -f apps/main exec:java
```

### run si-mock

> Démarre le mock du SI

```sh
echo "Clean des builds précédents"
rm -r apps/si-mock/target
echo "Compilation de l'application en cours..."
mask install
echo "Compilation terminée"
echo "Lancement du mock si..."
./mvnw -f apps/si-mock exec:java 
```

### run embedded-hal

> Démarre le embedded-hal

```sh
echo "Redémarrage du service dbus pour prévenir les erreurs en cas de mauvais arrêt."
sudo service dbus restart
echo "Lancement du embedded-hal ..."
embedded-hal mock 
```

## install

> Raccourci pour un mvn install optimisé, qui ne lance pas les tests

```sh
./mvnw -T 1C install -Dmaven.test.skip=true -DskipTests -q
```

## package

> Raccourci pour un package optimisé

```sh
echo "Packaging des applications..."
./mvnw clean -q
./mvnw -T 1C package -Dmaven.test.skip=true -DskipTests -q
echo "Terminé."
```

## test

> Lancement de tous les tests de l'application

```sh
mask install
./mvnw test
```

## sql

### sql exec (request)
> Permet de faire une commande SQl

```sh
sqlite3 /var/lib/i2r/i2r-params.db "$request"
```

### sql bipStatus

> Récupère le status du bip en BD

```sh
sqlite3 /var/lib/i2r/i2r-params.db "select * from parameters where param_name = 'i2r.bip.status';"
```
### sql select

> Fait un select sur la table 'parameters' de la BD

```sh
sqlite3 /var/lib/i2r/i2r-params.db "select * from parameters;"
```

## dev

### dev fill-test-data

> Remplit la base de données avec un minimum de données de test

```sh
echo "('v')/ remplissage de la base de données"
sqlite3 /var/lib/i2r/i2r-params.db \
 "CREATE TABLE IF NOT EXISTS parameters(param_name text not null primary key, param_value text not null, updated boolean);" \
 "INSERT INTO parameters(param_name, param_value) VALUES('i2r.si.primary-datacenter', 'pacy') ON CONFLICT(param_name) DO UPDATE SET param_value=excluded.param_value;" \
 "INSERT INTO parameters(param_name, param_value) VALUES('i2r.si.ip.pacy', '127.0.0.1') ON CONFLICT(param_name) DO UPDATE SET param_value=excluded.param_value;" \
 "INSERT INTO parameters(param_name, param_value) VALUES('i2r.si.ip.noe', '127.0.0.1') ON CONFLICT(param_name) DO UPDATE SET param_value=excluded.param_value;" \
 "INSERT INTO parameters(param_name, param_value) VALUES('i2r.bip.status', '1') ON CONFLICT(param_name) DO UPDATE SET param_value=excluded.param_value;"
```

## coverage

> Commandes pour afficher la couverture de code

### coverage run

> Lance les tests avec génération de la couverture et affiche les résultats

```sh
echo "Lancement des tests avec génération de la couverture..."
./mvnw test jacoco:report -q
echo ""
echo "Affichage de la couverture:"
echo ""
mask coverage show
```

### coverage show

> Affiche la couverture de code globale et par module (nécessite d'avoir exécuté les tests au préalable)

```sh
# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

printf "${BOLD}📊 COUVERTURE DE CODE${NC}\n"
echo "════════════════════════════════════════════════════════════════"

# Variables pour le calcul global
total_instructions=0
total_covered=0
module_count=0

# Parcourir tous les fichiers jacoco.csv
for csv_file in $(find . -name "jacoco.csv" -path "*/target/site/jacoco/*" 2>/dev/null | sort); do
    if [ -f "$csv_file" ]; then
        # Extraire le nom du module depuis le chemin
        module_path=$(echo "$csv_file" | sed 's|./||' | sed 's|/target/site/jacoco/jacoco.csv||')

        # Calculer la couverture pour ce module
        result=$(awk -F"," 'NR>1 { instructions += $4 + $5; covered += $5 } END {
            if (instructions > 0) {
                printf "%.1f;%d;%d", 100*covered/instructions, covered, instructions
            } else {
                print "0.0;0;0"
            }
        }' "$csv_file")

        # Séparer les résultats
        module_percentage=$(echo "$result" | cut -d';' -f1)
        module_covered=$(echo "$result" | cut -d';' -f2)
        module_instructions=$(echo "$result" | cut -d';' -f3)

        # Ajouter au total global
        total_covered=$((total_covered + module_covered))
        total_instructions=$((total_instructions + module_instructions))
        module_count=$((module_count + 1))

        # Choisir la couleur selon le pourcentage
        if [ $(echo "$module_percentage >= 80" | awk '{print ($1 >= 80)}') -eq 1 ]; then
            color=$GREEN
        elif [ $(echo "$module_percentage >= 60" | awk '{print ($1 >= 60)}') -eq 1 ]; then
            color=$YELLOW
        else
            color=$RED
        fi

        # Afficher la couverture du module avec couleur
        printf "%-35s ${color}%6s%%${NC} (%s/%s instructions)\n" "$module_path:" "$module_percentage" "$module_covered" "$module_instructions"
    fi
done

if [ $module_count -eq 0 ]; then
    printf "${RED}❌ Aucun rapport de couverture trouvé.${NC}\n"
    echo "Générez d'abord les rapports avec: mask coverage run"
    exit 1
fi

echo "────────────────────────────────────────────────────────────────"

# Calculer et afficher la couverture globale
if [ $total_instructions -gt 0 ]; then
    global_percentage=$(awk "BEGIN {printf \"%.1f\", 100 * $total_covered / $total_instructions}")

    # Choisir la couleur pour le total global
    if [ $(echo "$global_percentage >= 80" | awk '{print ($1 >= 80)}') -eq 1 ]; then
        color=$GREEN
    elif [ $(echo "$global_percentage >= 60" | awk '{print ($1 >= 60)}') -eq 1 ]; then
        color=$YELLOW
    else
        color=$RED
    fi

    printf "${BOLD}TOTAL GLOBAL:${NC}                   ${color}${BOLD}${global_percentage}%%${NC} (${total_covered}/${total_instructions} instructions)\n"
else
    printf "${BOLD}TOTAL GLOBAL:${NC}                   ${RED}${BOLD}0.0%%${NC} (0/0 instructions)\n"
fi

echo ""
```
